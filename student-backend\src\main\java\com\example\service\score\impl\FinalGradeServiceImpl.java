package com.example.service.score.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.common.PageResult;
import com.example.common.exception.BusinessException;
import com.example.common.export.config.ExportConfig;
import com.example.common.export.processor.DataProcessor;
import com.example.common.export.processor.GradeDataProcessor;
import com.example.common.export.service.AbstractExportImportService;
import com.example.dto.score.GradeQueryDTO;
import com.example.dto.score.GradeImportRequestDTO;
import com.example.entity.score.Grade;
import com.example.mapper.score.FinalGradeMapper;
import com.example.service.score.FinalGradeService;
import com.example.vo.educational.CourseVO;
import com.example.vo.score.GradeVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 期末成绩服务实现类
 * 重构后继承AbstractExportImportService，移除重复的导出导入代码
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FinalGradeServiceImpl extends AbstractExportImportService<Map<String, Object>, GradeImportRequestDTO>
        implements FinalGradeService {

    private final FinalGradeMapper finalGradeMapper;

    @Autowired
    private GradeDataProcessor gradeDataProcessor;

    @Override
    public PageResult<GradeVO> getGradePage(GradeQueryDTO params) {
        Page<GradeVO> page = new Page<>(params.getCurrent(), params.getSize());
        IPage<GradeVO> result = finalGradeMapper.selectGradePage(page, params);
        return new PageResult<>(result.getRecords(), result.getTotal(), (int) result.getSize(), (int) result.getCurrent());
    }

    @Override
    public List<GradeVO> getGradesByClass(String classCode, Integer semesterId) {
        if (!StringUtils.hasText(classCode)) {
            throw new BusinessException("班级代码不能为空");
        }
        return finalGradeMapper.selectGradesByClass(classCode, semesterId);
    }

    @Override
    public List<GradeVO> getGradesByStudent(String studentId, Integer semesterId) {
        if (!StringUtils.hasText(studentId)) {
            throw new BusinessException("学号不能为空");
        }
        return finalGradeMapper.selectGradesByStudent(studentId, semesterId);
    }

    @Override
    public Map<String, Object> getHorizontalGrades(String classCode, Integer semesterId, Integer pageNum, Integer pageSize) {
        if (!StringUtils.hasText(classCode)) {
            throw new BusinessException("班级代码不能为空");
        }

        try {
            Map<String, Object> result = new HashMap<>();

            // 1. 获取课程列表
            List<CourseVO> courses = finalGradeMapper.selectCoursesByClassAndSemester(classCode, semesterId);
            result.put("courses", courses);

            // 2. 获取学生基本信息和统计数据
            int offset = (pageNum - 1) * pageSize;
            List<Map<String, Object>> students = finalGradeMapper.selectStudentsWithGrades(classCode, semesterId, pageSize, offset);

            // 3. 获取所有成绩数据
            List<Map<String, Object>> allGrades = finalGradeMapper.selectGradesByStudentAndClass(classCode, semesterId);

            // 4. 获取学业成绩和绩点数据
            List<Map<String, Object>> academicData = finalGradeMapper.getStudentAcademicScoreAndGpa(classCode, semesterId);
            Map<String, Map<String, Object>> academicDataByStudent = academicData.stream()
                    .collect(Collectors.toMap(
                            data -> (String) data.get("studentId"),
                            data -> data,
                            (existing, replacement) -> existing
                    ));

            // 5. 将成绩数据按学生分组
            Map<String, Map<String, Object>> gradesByStudent = allGrades.stream()
                    .collect(Collectors.groupingBy(
                            grade -> (String) grade.get("studentId"),
                            Collectors.toMap(
                                    grade -> (String) grade.get("courseCode"),
                                    grade -> grade,
                                    (existing, replacement) -> existing
                            )
                    ));

            // 6. 为每个学生添加成绩信息、学业成绩和绩点
            students.forEach(student -> {
                String studentId = (String) student.get("studentId");
                Map<String, Object> studentGrades = gradesByStudent.getOrDefault(studentId, new HashMap<>());
                Map<String, Object> studentAcademicData = academicDataByStudent.get(studentId);

                Map<String, Object> grades = new HashMap<>();
                courses.forEach(course -> {
                    String courseCode = course.getCourseCode();
                    Map<String, Object> gradeInfo = (Map<String, Object>) studentGrades.get(courseCode);

                    if (gradeInfo != null) {
                        Object finalScore = gradeInfo.get("finalScore");
                        Boolean isRetake = (Boolean) gradeInfo.get("isRetake");

                        if (finalScore != null) {
                            grades.put(courseCode, Map.of(
                                    "finalScore", finalScore,
                                    "isRetake", isRetake != null ? isRetake : false
                            ));
                        }
                    }
                });

                student.put("grades", grades);

                // 添加学业成绩和绩点
                if (studentAcademicData != null) {
                    student.put("academicScore", studentAcademicData.get("academicScore"));
                    student.put("calculateGpa", studentAcademicData.get("calculateGpa"));
                } else {
                    student.put("academicScore", null);
                    student.put("calculateGpa", null);
                }
            });

            result.put("students", students);

            // 7. 获取总数
            Integer total = finalGradeMapper.selectStudentCountByClass(classCode);
            result.put("total", total);

            return result;

        } catch (Exception e) {
            log.error("获取横向期末成绩数据失败: classCode={}, semesterId={}", classCode, semesterId, e);
            throw new BusinessException("获取横向期末成绩数据失败: " + e.getMessage());
        }
    }



    // ==================== 重构后的导出方法 ====================

    @Override
    protected DataProcessor<Map<String, Object>, GradeImportRequestDTO> getDataProcessor() {
        return gradeDataProcessor;
    }

    @Override
    public List<Map<String, Object>> getExportData(Map<String, Object> queryParams) {
        String classCode = (String) queryParams.get("classCode");
        Object semesterIdObj = queryParams.get("semesterId");
        Object semesterIdsObj = queryParams.get("semesterIds");

        if (semesterIdObj != null) {
            // 单学期导出
            Integer semesterId = (Integer) semesterIdObj;
            return getSingleSemesterExportData(classCode, semesterId);
        } else if (semesterIdsObj != null) {
            // 多学期导出
            @SuppressWarnings("unchecked")
            List<Integer> semesterIds = (List<Integer>) semesterIdsObj;
            return getMultipleSemestersExportData(classCode, semesterIds);
        }

        throw new BusinessException("缺少必要的查询参数");
    }

    @Override
    public int saveImportData(List<GradeImportRequestDTO> data) {
        // 这里实现成绩导入的保存逻辑
        // 暂时返回0，具体实现需要根据业务需求
        return 0;
    }

    @Override
    public Class<Map<String, Object>> getExportDataType() {
        return (Class<Map<String, Object>>) (Class<?>) Map.class;
    }

    @Override
    public Class<GradeImportRequestDTO> getImportResultType() {
        return GradeImportRequestDTO.class;
    }

    @Override
    public byte[] exportGrades(String classCode, Integer semesterId) {
        try {
            log.info("开始导出单学期期末成绩: classCode={}, semesterId={}", classCode, semesterId);

            Map<String, Object> queryParams = new HashMap<>();
            queryParams.put("classCode", classCode);
            queryParams.put("semesterId", semesterId);

            ExportConfig config = ExportConfig.createGradeExportConfig(classCode);

            return exportToExcel(queryParams, config);

        } catch (Exception e) {
            log.error("导出单学期期末成绩失败: classCode={}, semesterId={}", classCode, semesterId, e);
            throw new BusinessException("导出单学期期末成绩失败: " + e.getMessage());
        }
    }

    @Override
    public byte[] exportMultipleSemestersGrades(String classCode, List<Integer> semesterIds) {
        try {
            log.info("开始导出多学期期末成绩: classCode={}, semesterIds={}", classCode, semesterIds);

            if (semesterIds == null || semesterIds.isEmpty()) {
                throw new BusinessException("学期ID列表不能为空");
            }

            Map<String, Object> queryParams = new HashMap<>();
            queryParams.put("classCode", classCode);
            queryParams.put("semesterIds", semesterIds);

            ExportConfig config = ExportConfig.createGradeExportConfig(classCode);

            return exportToExcel(queryParams, config);

        } catch (Exception e) {
            log.error("导出多学期期末成绩失败: classCode={}, semesterIds={}", classCode, semesterIds, e);
            throw new BusinessException("导出多学期期末成绩失败: " + e.getMessage());
        }
    }

    // ==================== 数据获取辅助方法 ====================

    /**
     * 获取单学期导出数据
     */
    private List<Map<String, Object>> getSingleSemesterExportData(String classCode, Integer semesterId) {
        try {
            // 获取横向成绩数据
            List<Map<String, Object>> horizontalGrades = finalGradeMapper.selectGradesByStudentAndClass(classCode, semesterId);

            if (horizontalGrades.isEmpty()) {
                log.warn("未找到成绩数据: classCode={}, semesterId={}", classCode, semesterId);
                return new ArrayList<>();
            }

            return horizontalGrades;

        } catch (Exception e) {
            log.error("获取单学期导出数据失败: classCode={}, semesterId={}", classCode, semesterId, e);
            throw new BusinessException("获取导出数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取多学期导出数据
     */
    private List<Map<String, Object>> getMultipleSemestersExportData(String classCode, List<Integer> semesterIds) {
        try {
            // 对学期ID进行升序排序
            List<Integer> sortedSemesterIds = new ArrayList<>(semesterIds);
            Collections.sort(sortedSemesterIds);

            // 一次性获取所有需要的数据
            Map<String, Object> allData = getAllExportData(classCode, sortedSemesterIds);

            @SuppressWarnings("unchecked")
            List<Map<String, Object>> students = (List<Map<String, Object>>) allData.get("students");
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> courses = (List<Map<String, Object>>) allData.get("courses");
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> grades = (List<Map<String, Object>>) allData.get("grades");
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> semesters = (List<Map<String, Object>>) allData.get("semesters");

            // 构建导出数据
            return buildMultipleSemestersExportData(students, courses, grades, semesters, sortedSemesterIds);

        } catch (Exception e) {
            log.error("获取多学期导出数据失败: classCode={}, semesterIds={}", classCode, semesterIds, e);
            throw new BusinessException("获取导出数据失败: " + e.getMessage());
        }
    }

    /**
     * 构建多学期导出数据
     */
    private List<Map<String, Object>> buildMultipleSemestersExportData(
            List<Map<String, Object>> students,
            List<Map<String, Object>> courses,
            List<Map<String, Object>> grades,
            List<Map<String, Object>> semesters,
            List<Integer> semesterIds) {

        List<Map<String, Object>> exportData = new ArrayList<>();

        // 构建学期名称映射
        Map<Integer, String> semesterNameMap = new HashMap<>();
        for (Map<String, Object> semester : semesters) {
            Integer id = (Integer) semester.get("id");
            String name = (String) semester.get("name");
            semesterNameMap.put(id, name);
        }

        // 构建课程映射
        Map<String, String> courseNameMap = new HashMap<>();
        for (Map<String, Object> course : courses) {
            String code = (String) course.get("courseCode");
            String name = (String) course.get("courseName");
            courseNameMap.put(code, name);
        }

        // 构建成绩映射 (学号_课程代码_学期ID -> 成绩)
        Map<String, Object> gradeMap = new HashMap<>();
        for (Map<String, Object> grade : grades) {
            String studentId = (String) grade.get("studentId");
            String courseCode = (String) grade.get("courseCode");
            Integer semesterId = (Integer) grade.get("semesterId");
            String key = studentId + "_" + courseCode + "_" + semesterId;
            gradeMap.put(key, grade.get("finalScore"));
        }

        // 为每个学生构建导出行
        for (Map<String, Object> student : students) {
            Map<String, Object> row = new LinkedHashMap<>();

            // 基本信息
            row.put("studentId", student.get("studentId"));
            row.put("studentName", student.get("studentName"));
            row.put("className", student.get("className"));

            // 为每个学期的每门课程添加成绩列
            for (Integer semesterId : semesterIds) {
                String semesterName = semesterNameMap.get(semesterId);

                // 获取该学期的课程
                Set<String> semesterCourses = new HashSet<>();
                for (Map<String, Object> grade : grades) {
                    if (semesterId.equals(grade.get("semesterId"))) {
                        semesterCourses.add((String) grade.get("courseCode"));
                    }
                }

                // 为每门课程添加成绩
                for (String courseCode : semesterCourses) {
                    String courseName = courseNameMap.get(courseCode);
                    String columnName = semesterName + "_" + courseName;

                    String gradeKey = student.get("studentId") + "_" + courseCode + "_" + semesterId;
                    Object score = gradeMap.get(gradeKey);
                    row.put(columnName, score != null ? score : "");
                }
            }

            exportData.add(row);
        }

        return exportData;
    }

    /**
     * 导出单个学期成绩（保留原有逻辑作为备用）
     */
    private byte[] exportSingleSemester(String classCode, Integer semesterId) {
        try {
            log.info("开始导出单学期期末成绩: classCode={}, semesterId={}", classCode, semesterId);

            // 获取班级名称
            String className = finalGradeMapper.getClassNameByCode(classCode);
            if (className == null) {
                className = classCode; // 如果找不到班级名称，使用班级代码
            }

            // 获取横向成绩数据
            Map<String, Object> horizontalData = getHorizontalGrades(classCode, semesterId, 1, Integer.MAX_VALUE);

            @SuppressWarnings("unchecked")
            List<Map<String, Object>> students = (List<Map<String, Object>>) horizontalData.get("students");

            if (students == null || students.isEmpty()) {
                throw new BusinessException("没有找到可导出的成绩数据");
            }

            // 获取课程列表
            @SuppressWarnings("unchecked")
            List<CourseVO> courses = (List<CourseVO>) horizontalData.get("courses");

            // 定义表头映射
            Map<String, String> headers = new LinkedHashMap<>();
            headers.put("studentId", "学号");
            headers.put("studentName", "姓名");

            // 添加课程成绩列，去除课程名中的学期信息
            if (courses != null) {
                for (CourseVO course : courses) {
                    String courseCode = course.getCourseCode();
                    String courseName = course.getCourseName();
                    headers.put(courseCode, courseName);
                }
            }

            // 将平均分、学业成绩、绩点放到最后
            headers.put("avgScore", "平均分");
            headers.put("academicScore", "学业成绩");
            headers.put("calculateGpa", "绩点");

            // 处理导出数据
            List<Map<String, Object>> exportData = new java.util.ArrayList<>();
            for (Map<String, Object> student : students) {
                Map<String, Object> row = new LinkedHashMap<>();

                // 基本信息
                row.put("studentId", student.get("studentId"));
                row.put("studentName", student.get("studentName"));

                // 处理课程成绩
                @SuppressWarnings("unchecked")
                Map<String, Object> grades = (Map<String, Object>) student.get("grades");
                if (grades != null && courses != null) {
                    for (CourseVO course : courses) {
                        String courseCode = course.getCourseCode();
                        Object gradeInfo = grades.get(courseCode);

                        if (gradeInfo instanceof Map) {
                            @SuppressWarnings("unchecked")
                            Map<String, Object> gradeMap = (Map<String, Object>) gradeInfo;
                            Object finalScore = gradeMap.get("finalScore");
                            Boolean isRetake = (Boolean) gradeMap.get("isRetake");

                            if (finalScore != null) {
                                String scoreText = finalScore.toString();
                                if (isRetake != null && isRetake) {
                                    scoreText += "(重修)";
                                }
                                row.put(courseCode, scoreText);
                            } else {
                                row.put(courseCode, "");
                            }
                        } else {
                            row.put(courseCode, "");
                        }
                    }
                 }

                 // 将平均分、学业成绩、绩点放到最后
                 row.put("avgScore", student.get("avgScore"));
                 row.put("academicScore", student.get("academicScore"));
                 row.put("calculateGpa", student.get("calculateGpa"));

                 exportData.add(row);
            }

            // 使用Apache POI生成Excel文件
            String sheetName = className + "学业成绩";
            byte[] excelData = apachePoiExportService.exportGradesToExcel(exportData, headers, sheetName);

            log.info("单学期期末成绩导出成功（Apache POI），数据大小: {} bytes", excelData.length);
            return excelData;

        } catch (Exception e) {
            log.error("导出单学期期末成绩失败: classCode={}, semesterId={}", classCode, semesterId, e);
            throw new BusinessException("导出单学期期末成绩失败: " + e.getMessage());
        }
    }

    /**
     * 导出多学期合并成绩
     */
    private byte[] exportMultipleSemesters(String classCode, List<Integer> semesterIds) {
        try {
            log.info("开始导出多学期合并期末成绩: classCode={}, semesterIds={}", classCode, semesterIds);

            // 对学期ID进行升序排序
            List<Integer> sortedSemesterIds = new ArrayList<>(semesterIds);
            Collections.sort(sortedSemesterIds);

            // 获取班级名称
            String className = finalGradeMapper.getClassNameByCode(classCode);
            if (className == null) {
                className = classCode;
            }

            // 获取所有学期的学生列表（去重）
            Set<String> allStudentIds = new HashSet<>();
            Map<String, String> studentNames = new HashMap<>();

            for (Integer semesterId : sortedSemesterIds) {
                List<Map<String, Object>> students = finalGradeMapper.selectStudentsWithGrades(classCode, semesterId, Integer.MAX_VALUE, 0);
                for (Map<String, Object> student : students) {
                    String studentId = (String) student.get("studentId");
                    String studentName = (String) student.get("studentName");
                    allStudentIds.add(studentId);
                    studentNames.put(studentId, studentName);
                }
            }

            // 获取所有学期的课程列表（去重）
            Set<String> allCourseCodes = new HashSet<>();
            Map<String, String> courseNames = new HashMap<>();

            for (Integer semesterId : sortedSemesterIds) {
                List<CourseVO> courses = finalGradeMapper.selectCoursesByClassAndSemester(classCode, semesterId);
                for (CourseVO course : courses) {
                    allCourseCodes.add(course.getCourseCode());
                    courseNames.put(course.getCourseCode(), course.getCourseName());
                }
            }

            // 获取学期名称映射
            Map<Integer, String> semesterNames = new HashMap<>();
            for (Integer semesterId : sortedSemesterIds) {
                String semesterName = finalGradeMapper.getSemesterNameById(semesterId);
                semesterNames.put(semesterId, semesterName != null ? semesterName : "学期" + semesterId);
            }

            // 构建表头
            Map<String, String> headers = new LinkedHashMap<>();
            headers.put("studentId", "学号");
            headers.put("studentName", "姓名");

            // 为每个学期的每门课程添加列（按学期升序）
            for (Integer semesterId : sortedSemesterIds) {
                String semesterName = semesterNames.get(semesterId);
                List<CourseVO> semesterCourses = finalGradeMapper.selectCoursesByClassAndSemester(classCode, semesterId);

                for (CourseVO course : semesterCourses) {
                    String columnKey = "semester_" + semesterId + "_course_" + course.getCourseCode();
                    // 去除课程名中的学期信息，只保留课程名
                    String columnName = course.getCourseName();
                    headers.put(columnKey, columnName);
                }
            }

            // 添加统计字段到表头最后
            headers.put("avgScore", "平均分");
            headers.put("academicScore", "学业成绩");
            headers.put("calculateGpa", "绩点");

            // 构建导出数据
            List<Map<String, Object>> exportData = new ArrayList<>();

            // 将学生ID列表转换为有序列表并按学号升序排序
            List<String> sortedStudentIds = new ArrayList<>(allStudentIds);
            Collections.sort(sortedStudentIds);

            // 获取所有学期的学生统计数据
            List<Map<String, Object>> allStudentStats = finalGradeMapper.getStudentAcademicScoreAndGpa(classCode, null);
            if (allStudentStats == null || allStudentStats.isEmpty()) {
                throw new BusinessException("没有找到学生的统计数据");
            }

            Map<String, Map<String, Object>> studentStatsMap = allStudentStats.stream()
                    .collect(Collectors.toMap(
                            stats -> (String) stats.get("studentId"),
                            stats -> stats,
                            (existing, replacement) -> {
                                // 如果存在重复的学生ID，保留现有的数据
                                return existing;
                            }
                    ));

            for (String studentId : sortedStudentIds) {
                Map<String, Object> row = new LinkedHashMap<>();
                row.put("studentId", studentId);
                row.put("studentName", studentNames.get(studentId));

                // 为每个学期获取该学生的成绩（按学期升序）
                for (Integer semesterId : sortedSemesterIds) {
                    List<Map<String, Object>> studentGrades = finalGradeMapper.selectGradesByStudentAndClass(classCode, semesterId);
                    Map<String, Object> gradesByStudent = studentGrades.stream()
                            .filter(grade -> studentId.equals(grade.get("studentId")))
                            .collect(Collectors.toMap(
                                    grade -> (String) grade.get("courseCode"),
                                    grade -> grade,
                                    (existing, replacement) -> existing
                            ));

                    List<CourseVO> semesterCourses = finalGradeMapper.selectCoursesByClassAndSemester(classCode, semesterId);
                    for (CourseVO course : semesterCourses) {
                        String columnKey = "semester_" + semesterId + "_course_" + course.getCourseCode();
                        Map<String, Object> gradeInfo = (Map<String, Object>) gradesByStudent.get(course.getCourseCode());

                        if (gradeInfo != null) {
                            Object finalScore = gradeInfo.get("finalScore");
                            Boolean isRetake = (Boolean) gradeInfo.get("isRetake");

                            if (finalScore != null) {
                                String scoreText = finalScore.toString();
                                if (isRetake != null && isRetake) {
                                    scoreText += "(重修)";
                                }
                                row.put(columnKey, scoreText);
                            } else {
                                row.put(columnKey, "");
                            }
                        } else {
                            row.put(columnKey, "");
                        }
                    }
                }

                // 添加统计字段
                Map<String, Object> studentStats = studentStatsMap.get(studentId);
                if (studentStats == null) {
                    // 如果找不到学生的统计数据，记录日志并设置默认值
                    log.warn("找不到学生ID为 {} 的统计数据", studentId);
                    row.put("avgScore", "");
                    row.put("academicScore", "");
                    row.put("calculateGpa", "");
                } else {
                    // 处理可能为null的值，确保导出的数据不为null
                    Object avgScore = studentStats.get("avgScore");
                    Object academicScore = studentStats.get("academicScore");
                    Object calculateGpa = studentStats.get("calculateGpa");

                    row.put("avgScore", avgScore != null ? avgScore : "");
                    row.put("academicScore", academicScore != null ? academicScore : "");
                    row.put("calculateGpa", calculateGpa != null ? calculateGpa : "");
                }

                exportData.add(row);
            }

            // 使用Apache POI生成Excel文件
            String sheetName = className + "学业成绩";
            byte[] excelData = apachePoiExportService.exportGradesToExcel(exportData, headers, sheetName);

            log.info("多学期合并期末成绩导出成功（Apache POI），数据大小: {} bytes", excelData.length);
            return excelData;

        } catch (Exception e) {
            log.error("导出多学期合并期末成绩失败: classCode={}, semesterIds={}", classCode, semesterIds, e);
            throw new BusinessException("导出多学期合并期末成绩失败: " + e.getMessage());
        }
    }

    /**
     * 优化的统一导出方法 - 使用Apache POI，减少数据库查询，支持百万行数据
     */
    private byte[] exportGradesOptimized(String classCode, List<Integer> semesterIds, String className) {
        try {
            log.info("开始优化导出成绩: classCode={}, semesterIds={}", classCode, semesterIds);

            // 对学期ID进行升序排序
            List<Integer> sortedSemesterIds = new ArrayList<>(semesterIds);
            Collections.sort(sortedSemesterIds);

            // 一次性获取所有需要的数据
            Map<String, Object> allData = getAllExportData(classCode, sortedSemesterIds);

            @SuppressWarnings("unchecked")
            List<Map<String, Object>> students = (List<Map<String, Object>>) allData.get("students");
            @SuppressWarnings("unchecked")
            Map<String, String> semesterNames = (Map<String, String>) allData.get("semesterNames");
            @SuppressWarnings("unchecked")
            Map<Integer, List<CourseVO>> coursesBySemester = (Map<Integer, List<CourseVO>>) allData.get("coursesBySemester");

            if (students == null || students.isEmpty()) {
                throw new BusinessException("没有找到可导出的成绩数据");
            }

            // 构建表头
            Map<String, String> headers = buildOptimizedHeaders(sortedSemesterIds, coursesBySemester, semesterNames);

            // 构建导出数据
            List<Map<String, Object>> exportData = buildOptimizedExportData(students, sortedSemesterIds, coursesBySemester, classCode);

            // 使用Apache POI生成Excel文件
            String sheetName = className + "学业成绩";
            byte[] excelData = apachePoiExportService.exportGradesToExcel(exportData, headers, sheetName);

            log.info("优化导出成功（Apache POI），学期数: {}, 学生数: {}, 文件大小: {} bytes",
                    sortedSemesterIds.size(), students.size(), excelData.length);
            return excelData;

        } catch (Exception e) {
            log.error("优化导出失败: classCode={}, semesterIds={}", classCode, semesterIds, e);
            throw new BusinessException("优化导出失败: " + e.getMessage());
        }
    }

    /**
     * 一次性获取所有导出需要的数据
     */
    private Map<String, Object> getAllExportData(String classCode, List<Integer> semesterIds) {
        Map<String, Object> result = new HashMap<>();

        // 获取所有学生的成绩和统计数据
        // 如果是多学期，传null获取所有学期的数据；如果是单学期，传具体学期ID
        Integer semesterParam = semesterIds.size() == 1 ? semesterIds.get(0) : null;
        List<Map<String, Object>> allStudentStats = finalGradeMapper.getStudentAcademicScoreAndGpa(classCode, semesterParam);
        result.put("students", allStudentStats);

        // 获取学期名称映射
        Map<String, String> semesterNames = new HashMap<>();
        for (Integer semesterId : semesterIds) {
            String semesterName = finalGradeMapper.getSemesterNameById(semesterId);
            semesterNames.put(String.valueOf(semesterId), semesterName != null ? semesterName : "学期" + semesterId);
        }
        result.put("semesterNames", semesterNames);

        // 获取每个学期的课程列表
        Map<Integer, List<CourseVO>> coursesBySemester = new HashMap<>();
        for (Integer semesterId : semesterIds) {
            List<CourseVO> courses = finalGradeMapper.selectCoursesByClassAndSemester(classCode, semesterId);
            coursesBySemester.put(semesterId, courses);
        }
        result.put("coursesBySemester", coursesBySemester);

        return result;
    }

    /**
     * 构建优化的表头
     */
    private Map<String, String> buildOptimizedHeaders(List<Integer> semesterIds,
                                                     Map<Integer, List<CourseVO>> coursesBySemester,
                                                     Map<String, String> semesterNames) {
        Map<String, String> headers = new LinkedHashMap<>();
        headers.put("studentId", "学号");
        headers.put("studentName", "姓名");

        // 为每个学期的每门课程添加列
        for (Integer semesterId : semesterIds) {
            List<CourseVO> courses = coursesBySemester.get(semesterId);
            if (courses != null) {
                for (CourseVO course : courses) {
                    String columnKey = "semester_" + semesterId + "_course_" + course.getCourseCode();
                    String columnName = course.getCourseName();
                    headers.put(columnKey, columnName);
                }
            }
        }

        // 添加统计字段
        headers.put("avgScore", "平均分");
        headers.put("academicScore", "学业成绩");
        headers.put("calculateGpa", "绩点");

        return headers;
    }

    /**
     * 构建优化的导出数据
     */
    private List<Map<String, Object>> buildOptimizedExportData(List<Map<String, Object>> students,
                                                              List<Integer> semesterIds,
                                                              Map<Integer, List<CourseVO>> coursesBySemester,
                                                              String classCode) {
        List<Map<String, Object>> exportData = new ArrayList<>();

        // 一次性获取所有学期的成绩数据
        Map<String, Map<String, Map<String, Object>>> allGradesMap = new HashMap<>();
        for (Integer semesterId : semesterIds) {
            List<Map<String, Object>> semesterGrades = finalGradeMapper.selectGradesByStudentAndClass(classCode, semesterId);
            for (Map<String, Object> grade : semesterGrades) {
                String studentId = (String) grade.get("studentId");
                String courseCode = (String) grade.get("courseCode");

                allGradesMap.computeIfAbsent(studentId, k -> new HashMap<>())
                           .put(semesterId + "_" + courseCode, grade);
            }
        }

        for (Map<String, Object> student : students) {
            Map<String, Object> row = new LinkedHashMap<>();

            // 基本信息
            row.put("studentId", student.get("studentId"));
            row.put("studentName", student.get("studentName"));

            String studentId = (String) student.get("studentId");
            Map<String, Map<String, Object>> studentGrades = allGradesMap.get(studentId);

            // 为每个学期的每门课程填充成绩
            for (Integer semesterId : semesterIds) {
                List<CourseVO> courses = coursesBySemester.get(semesterId);
                if (courses != null) {
                    for (CourseVO course : courses) {
                        String columnKey = "semester_" + semesterId + "_course_" + course.getCourseCode();
                        String gradeKey = semesterId + "_" + course.getCourseCode();

                        if (studentGrades != null && studentGrades.containsKey(gradeKey)) {
                            Map<String, Object> gradeInfo = studentGrades.get(gradeKey);
                            Object finalScore = gradeInfo.get("finalScore");
                            Boolean isRetake = (Boolean) gradeInfo.get("isRetake");

                            if (finalScore != null) {
                                String scoreText = finalScore.toString();
                                if (isRetake != null && isRetake) {
                                    scoreText += "(重修)";
                                }
                                row.put(columnKey, scoreText);
                            } else {
                                row.put(columnKey, "");
                            }
                        } else {
                            row.put(columnKey, "");
                        }
                    }
                }
            }

            // 添加统计数据
            row.put("avgScore", student.get("avgScore") != null ? student.get("avgScore") : "");
            row.put("academicScore", student.get("academicScore") != null ? student.get("academicScore") : "");
            row.put("calculateGpa", student.get("calculateGpa") != null ? student.get("calculateGpa") : "");

            exportData.add(row);
        }

        return exportData;
    }

    @Override
    public String getClassNameByCode(String classCode) {
        if (!StringUtils.hasText(classCode)) {
            throw new BusinessException("班级代码不能为空");
        }
        return finalGradeMapper.getClassNameByCode(classCode);
    }
}
