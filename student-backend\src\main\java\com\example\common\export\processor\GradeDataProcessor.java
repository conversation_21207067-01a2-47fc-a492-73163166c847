package com.example.common.export.processor;

import com.example.common.export.config.ExportConfig;
import com.example.common.export.config.ImportConfig;
import com.example.dto.score.GradeImportRequestDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 成绩数据处理器
 * 专门处理成绩相关的数据转换和验证逻辑
 *
 * <AUTHOR>
 * @since 2025-01-30
 */
@Component
@Slf4j
public class GradeDataProcessor implements DataProcessor<Map<String, Object>, GradeImportRequestDTO> {

    @Override
    public List<Map<String, Object>> processExportData(List<Map<String, Object>> data, ExportConfig config) {
        log.info("开始处理成绩导出数据，数据量: {}", data.size());
        
        List<Map<String, Object>> exportData = new ArrayList<>();
        
        for (Map<String, Object> item : data) {
            Map<String, Object> row = new LinkedHashMap<>();
            
            // 按照配置的表头顺序处理数据
            for (Map.Entry<String, String> header : config.getHeaders().entrySet()) {
                String fieldName = header.getKey();
                Object value = item.get(fieldName);
                
                // 特殊处理成绩和绩点字段，保留2位小数
                if (isScoreOrGpaField(fieldName) && value instanceof Number) {
                    BigDecimal decimal = new BigDecimal(value.toString());
                    row.put(fieldName, decimal.setScale(2, BigDecimal.ROUND_HALF_UP));
                } else {
                    row.put(fieldName, value != null ? value : "");
                }
            }
            
            exportData.add(row);
        }
        
        log.info("成绩导出数据处理完成，处理后数据量: {}", exportData.size());
        return exportData;
    }

    @Override
    public List<GradeImportRequestDTO> processImportData(List<Map<String, Object>> data, ImportConfig config) {
        log.info("开始处理成绩导入数据，数据量: {}", data.size());
        
        List<GradeImportRequestDTO> importDTOs = new ArrayList<>();
        
        for (int i = 0; i < data.size(); i++) {
            Map<String, Object> rowData = data.get(i);
            GradeImportRequestDTO dto = new GradeImportRequestDTO();
            dto.setRowNumber(i + 2); // Excel行号从2开始（第1行是表头）
            
            try {
                // 学号
                Object studentIdObj = rowData.get("studentId");
                if (studentIdObj != null) {
                    dto.setStudentId(studentIdObj.toString().trim());
                }
                
                // 课程代码
                Object courseCodeObj = rowData.get("courseCode");
                if (courseCodeObj != null) {
                    dto.setCourseCode(courseCodeObj.toString().trim());
                }
                
                // 学期ID
                Object semesterIdObj = rowData.get("semesterId");
                if (semesterIdObj != null) {
                    try {
                        if (semesterIdObj instanceof Number) {
                            dto.setSemesterId(((Number) semesterIdObj).intValue());
                        } else {
                            dto.setSemesterId(Integer.parseInt(semesterIdObj.toString().trim()));
                        }
                    } catch (NumberFormatException e) {
                        log.warn("第{}行学期ID格式错误: {}", i + 2, semesterIdObj);
                    }
                }
                
                // 期末成绩
                Object finalScoreObj = rowData.get("finalScore");
                if (finalScoreObj != null) {
                    try {
                        if (finalScoreObj instanceof Number) {
                            dto.setFinalScore(new BigDecimal(finalScoreObj.toString()));
                        } else {
                            String scoreStr = finalScoreObj.toString().trim();
                            if (!scoreStr.isEmpty()) {
                                dto.setFinalScore(new BigDecimal(scoreStr));
                            }
                        }
                    } catch (NumberFormatException e) {
                        log.warn("第{}行期末成绩格式错误: {}", i + 2, finalScoreObj);
                    }
                }
                
                // 备注
                Object remarksObj = rowData.get("remarks");
                if (remarksObj != null) {
                    dto.setRemarks(remarksObj.toString().trim());
                }
                
                importDTOs.add(dto);
                
            } catch (Exception e) {
                log.warn("转换第{}行数据失败: {}", i + 2, e.getMessage());
                // 仍然添加到列表中，让验证阶段处理错误
                importDTOs.add(dto);
            }
        }
        
        log.info("成绩导入数据处理完成，处理后数据量: {}", importDTOs.size());
        return importDTOs;
    }

    @Override
    public ValidationResult validateImportData(GradeImportRequestDTO data, ImportConfig config) {
        List<String> errors = new ArrayList<>();
        
        // 验证必填字段
        if (!StringUtils.hasText(data.getStudentId())) {
            errors.add("学号不能为空");
        }
        
        if (!StringUtils.hasText(data.getCourseCode())) {
            errors.add("课程代码不能为空");
        }
        
        if (data.getSemesterId() == null) {
            errors.add("学期ID不能为空");
        }
        
        if (data.getFinalScore() == null) {
            errors.add("期末成绩不能为空");
        } else {
            // 验证成绩范围
            BigDecimal score = data.getFinalScore();
            if (score.compareTo(BigDecimal.ZERO) < 0 || score.compareTo(new BigDecimal("100")) > 0) {
                errors.add("期末成绩必须在0-100之间");
            }
        }
        
        // 验证学号格式（假设学号为数字）
        if (StringUtils.hasText(data.getStudentId())) {
            try {
                Long.parseLong(data.getStudentId());
            } catch (NumberFormatException e) {
                errors.add("学号格式错误，必须为数字");
            }
        }
        
        if (!errors.isEmpty()) {
            return ValidationResult.error(String.join("; ", errors), data.getRowNumber());
        }
        
        return ValidationResult.success();
    }

    @Override
    public Class<Map<String, Object>> getSupportedType() {
        return (Class<Map<String, Object>>) (Class<?>) Map.class;
    }

    /**
     * 判断是否为学业成绩或绩点字段
     */
    private boolean isScoreOrGpaField(String fieldName) {
        if (fieldName == null) return false;
        String lowerFieldName = fieldName.toLowerCase();
        return lowerFieldName.contains("score") ||
               lowerFieldName.contains("grade") ||
               lowerFieldName.contains("gpa") ||
               lowerFieldName.contains("绩点") ||
               lowerFieldName.contains("成绩") ||
               lowerFieldName.contains("分数");
    }
}
