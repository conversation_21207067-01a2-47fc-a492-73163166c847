package com.example.common.export.processor;

import com.example.common.export.config.ExportConfig;
import com.example.common.export.config.ImportConfig;
import com.example.vo.student.StudentsVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 学生数据处理器
 * 专门处理学生相关的数据转换和验证逻辑
 *
 * <AUTHOR>
 * @since 2025-01-30
 */
@Component
@Slf4j
public class StudentDataProcessor implements DataProcessor<StudentsVO, StudentsVO> {

    @Override
    public List<Map<String, Object>> processExportData(List<StudentsVO> data, ExportConfig config) {
        log.info("开始处理学生导出数据，数据量: {}", data.size());

        List<Map<String, Object>> exportData = new ArrayList<>();

        for (StudentsVO student : data) {
            Map<String, Object> row = new LinkedHashMap<>();

            // 按照配置的表头顺序处理数据
            for (Map.Entry<String, String> header : config.getHeaders().entrySet()) {
                String fieldName = header.getKey();
                Object value = getFieldValue(student, fieldName);
                row.put(fieldName, value != null ? value : "");
            }

            exportData.add(row);
        }

        log.info("学生导出数据处理完成，处理后数据量: {}", exportData.size());
        return exportData;
    }

    @Override
    public List<StudentsVO> processImportData(List<Map<String, Object>> data, ImportConfig config) {
        log.info("开始处理学生导入数据，数据量: {}", data.size());

        List<StudentsVO> importStudents = new ArrayList<>();

        for (int i = 0; i < data.size(); i++) {
            Map<String, Object> rowData = data.get(i);
            StudentsVO student = new StudentsVO();

            try {
                // 学号
                Object studentIdObj = rowData.get("studentId");
                if (studentIdObj != null) {
                    student.setStudentId(studentIdObj.toString().trim());
                }

                // 姓名
                Object studentNameObj = rowData.get("studentName");
                if (studentNameObj != null) {
                    student.setName(studentNameObj.toString().trim());
                }

                // 性别
                Object genderObj = rowData.get("gender");
                if (genderObj != null) {
                    student.setGender(genderObj.toString().trim());
                }

                // 班级
                Object classNameObj = rowData.get("className");
                if (classNameObj != null) {
                    student.setClassName(classNameObj.toString().trim());
                }

                // 电话
                Object phoneObj = rowData.get("phone");
                if (phoneObj != null) {
                    student.setPhone(phoneObj.toString().trim());
                }

                // 邮箱
                Object emailObj = rowData.get("email");
                if (emailObj != null) {
                    student.setEmail(emailObj.toString().trim());
                }

                importStudents.add(student);

            } catch (Exception e) {
                log.warn("转换第{}行学生数据失败: {}", i + 2, e.getMessage());
                // 仍然添加到列表中，让验证阶段处理错误
                importStudents.add(student);
            }
        }

        log.info("学生导入数据处理完成，处理后数据量: {}", importStudents.size());
        return importStudents;
    }

    @Override
    public ValidationResult validateImportData(StudentsVO data, ImportConfig config) {
        List<String> errors = new ArrayList<>();

        // 验证必填字段
        if (!StringUtils.hasText(data.getStudentId())) {
            errors.add("学号不能为空");
        }

        if (!StringUtils.hasText(data.getName())) {
            errors.add("姓名不能为空");
        }

        if (!StringUtils.hasText(data.getGender())) {
            errors.add("性别不能为空");
        }

        if (!StringUtils.hasText(data.getClassName())) {
            errors.add("班级不能为空");
        }

        // 验证学号格式（假设学号为数字）
        if (StringUtils.hasText(data.getStudentId())) {
            try {
                Long.parseLong(data.getStudentId());
            } catch (NumberFormatException e) {
                errors.add("学号格式错误，必须为数字");
            }
        }

        // 验证性别
        if (StringUtils.hasText(data.getGender())) {
            if (!"男".equals(data.getGender()) && !"女".equals(data.getGender())) {
                errors.add("性别只能是'男'或'女'");
            }
        }

        // 验证邮箱格式
        if (StringUtils.hasText(data.getEmail())) {
            if (!isValidEmail(data.getEmail())) {
                errors.add("邮箱格式错误");
            }
        }

        // 验证手机号格式
        if (StringUtils.hasText(data.getPhone())) {
            if (!isValidPhone(data.getPhone())) {
                errors.add("手机号格式错误");
            }
        }

        if (!errors.isEmpty()) {
            return ValidationResult.error(String.join("; ", errors), 0);
        }

        return ValidationResult.success();
    }

    @Override
    public Class<StudentsVO> getSupportedType() {
        return StudentsVO.class;
    }

    /**
     * 根据字段名获取学生对象的字段值
     */
    private Object getFieldValue(StudentsVO student, String fieldName) {
        switch (fieldName) {
            case "studentId":
                return student.getStudentId();
            case "studentName":
                return student.getName();
            case "gender":
                return student.getGender();
            case "className":
                return student.getClassName();
            case "majorName":
                return student.getMajorName();
            case "collegeName":
                return student.getCollegeName();
            case "phone":
                return student.getPhone();
            case "email":
                return student.getEmail();
            default:
                return null;
        }
    }

    /**
     * 验证邮箱格式
     */
    private boolean isValidEmail(String email) {
        return email.matches("^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$");
    }

    /**
     * 验证手机号格式
     */
    private boolean isValidPhone(String phone) {
        return phone.matches("^1[3-9]\\d{9}$");
    }
}
