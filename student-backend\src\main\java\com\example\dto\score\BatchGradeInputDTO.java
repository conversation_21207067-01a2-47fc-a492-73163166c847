package com.example.dto.score;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * 批量成绩录入DTO
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
@Schema(description = "批量成绩录入DTO")
public class BatchGradeInputDTO {

    @Schema(description = "班级代码", required = true)
    @NotBlank(message = "班级代码不能为空")
    private String classCode;

    @Schema(description = "课程代码", required = true)
    @NotBlank(message = "课程代码不能为空")
    private String courseCode;

    @Schema(description = "学期ID", required = true)
    @NotNull(message = "学期ID不能为空")
    private Integer semesterId;

    @Schema(description = "成绩列表", required = true)
    @NotEmpty(message = "成绩列表不能为空")
    @Valid
    private List<GradeInputDTO> grades;
}
