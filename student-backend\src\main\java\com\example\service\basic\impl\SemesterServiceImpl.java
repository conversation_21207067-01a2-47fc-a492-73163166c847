package com.example.service.basic.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.common.exception.BusinessException;
import com.example.common.PageResult;
import com.example.dto.basic.SemesterQueryDTO;
import com.example.entity.basic.Semester;
import com.example.mapper.basic.SemesterMapper;
import com.example.service.basic.SemesterService;
import com.example.vo.basic.SemesterVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 学年学期服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-27
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SemesterServiceImpl implements SemesterService {

    private final SemesterMapper semesterMapper;

    @Override
    @Cacheable(value = "semesterList", key = "#query.toString()")
    public PageResult<SemesterVO> getSemesterList(SemesterQueryDTO query) {
        if (query == null) {
            query = new SemesterQueryDTO();
        }

        // 设置默认分页参数
        if (query.getCurrent() == null || query.getCurrent() <= 0) {
            query.setCurrent(1);
        }
        if (query.getSize() == null || query.getSize() <= 0) {
            query.setSize(10);
        }

        IPage<Semester> page = new Page<>(query.getCurrent(), query.getSize());
        IPage<Semester> semesterPage = semesterMapper.selectSemesterPage(page, query);

        List<SemesterVO> semesterVOList = semesterPage.getRecords().stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());

        return PageResult.<SemesterVO>builder()
                .list(semesterVOList)
                .total(semesterPage.getTotal())
                .pageNum(query.getCurrent())
                .pageSize(query.getSize())
                .build();
    }

    @Override
    @Cacheable(value = "allSemesters")
    public List<SemesterVO> getAllSemesters() {
        List<Semester> semesters = semesterMapper.selectAllSemesters();
        return semesters.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    @Override
    @Cacheable(value = "semestersByYear", key = "#academicYear")
    public List<SemesterVO> getSemestersByAcademicYear(String academicYear) {
        if (!StringUtils.hasText(academicYear)) {
            throw new BusinessException("学年不能为空");
        }

        List<Semester> semesters = semesterMapper.selectByAcademicYear(academicYear);
        return semesters.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    @Override
    @Cacheable(value = "semesterDetail", key = "#id")
    public SemesterVO getSemesterById(Integer id) {
        if (id == null) {
            throw new BusinessException("学期ID不能为空");
        }

        Semester semester = semesterMapper.selectById(id);
        if (semester == null) {
            throw new BusinessException("学期不存在");
        }

        return convertToVO(semester);
    }

    @Override
    @Cacheable(value = "currentSemester")
    public SemesterVO getCurrentSemester() {
        Semester semester = semesterMapper.selectCurrentSemester();
        if (semester == null) {
            return null;
        }
        return convertToVO(semester);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = {"semesterList", "allSemesters", "semestersByYear", "semesterDetail", "currentSemester"}, allEntries = true)
    public void saveSemester(SemesterVO semesterVO) {
        if (semesterVO == null) {
            throw new BusinessException("学期信息不能为空");
        }

        // 验证学期信息
        validateSemesterInfo(semesterVO, null);

        try {
            Semester semester = new Semester();
            copyVOToEntity(semesterVO, semester);

            // 设置默认值
            if (semester.getIsCurrent() == null) {
                semester.setIsCurrent(false);
            }

            semester.setCreatedAt(LocalDateTime.now());
            semester.setUpdatedAt(LocalDateTime.now());

            semesterMapper.insert(semester);

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("新增学期失败: {}", semesterVO.getSemesterName(), e);
            throw new BusinessException("新增学期失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = {"semesterList", "allSemesters", "semestersByYear", "semesterDetail", "currentSemester"}, allEntries = true)
    public void updateSemester(SemesterVO semesterVO) {
        if (semesterVO == null || semesterVO.getId() == null) {
            throw new BusinessException("学期信息不能为空");
        }

        // 检查学期是否存在
        Semester existingSemester = semesterMapper.selectById(semesterVO.getId());
        if (existingSemester == null) {
            throw new BusinessException("学期不存在");
        }

        // 验证学期信息
        validateSemesterInfo(semesterVO, semesterVO.getId());

        try {
            Semester semester = new Semester();
            copyVOToEntity(semesterVO, semester);
            semester.setId(semesterVO.getId());
            semester.setUpdatedAt(LocalDateTime.now());

            semesterMapper.updateById(semester);

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("更新学期失败: {}", semesterVO.getSemesterName(), e);
            throw new BusinessException("更新学期失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = {"semesterList", "allSemesters", "semestersByYear", "semesterDetail", "currentSemester"}, allEntries = true)
    public void deleteSemester(Integer id) {
        if (id == null) {
            throw new BusinessException("学期ID不能为空");
        }

        Semester semester = semesterMapper.selectById(id);
        if (semester == null) {
            throw new BusinessException("学期不存在");
        }

        // 检查是否为当前学期
        if (Boolean.TRUE.equals(semester.getIsCurrent())) {
            throw new BusinessException("不能删除当前学期");
        }

        try {
            semesterMapper.deleteById(id);
        } catch (Exception e) {
            log.error("删除学期失败: {}", semester.getSemesterName(), e);
            throw new BusinessException("删除学期失败，可能存在关联数据");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = {"semesterList", "allSemesters", "semestersByYear", "semesterDetail", "currentSemester"}, allEntries = true)
    public void setCurrentSemester(Integer id) {
        if (id == null) {
            throw new BusinessException("学期ID不能为空");
        }

        Semester semester = semesterMapper.selectById(id);
        if (semester == null) {
            throw new BusinessException("学期不存在");
        }

        try {
            // 清除所有当前学期标记
            semesterMapper.clearAllCurrentFlags();
            // 设置新的当前学期
            semesterMapper.setCurrentSemester(id);
        } catch (Exception e) {
            log.error("设置当前学期失败: {}", semester.getSemesterName(), e);
            throw new BusinessException("设置当前学期失败");
        }
    }

    /**
     * 验证学期信息
     */
    private void validateSemesterInfo(SemesterVO semesterVO, Integer excludeId) {
        // 检查学年学期组合是否重复
        if (StringUtils.hasText(semesterVO.getAcademicYear()) && semesterVO.getSemesterNumber() != null) {
            Boolean exists = semesterMapper.existsByAcademicYearAndNumber(
                    semesterVO.getAcademicYear(), semesterVO.getSemesterNumber(), excludeId);
            if (Boolean.TRUE.equals(exists)) {
                throw new BusinessException("该学年学期已存在");
            }
        }

        // 检查学期名称是否重复
        if (StringUtils.hasText(semesterVO.getSemesterName())) {
            Boolean exists = semesterMapper.existsBySemesterName(semesterVO.getSemesterName(), excludeId);
            if (Boolean.TRUE.equals(exists)) {
                throw new BusinessException("学期名称已存在");
            }
        }

        // 验证日期逻辑
        if (semesterVO.getStartDate() != null && semesterVO.getEndDate() != null) {
            if (semesterVO.getStartDate().isAfter(semesterVO.getEndDate())) {
                throw new BusinessException("开始日期不能晚于结束日期");
            }
        }
    }

    /**
     * 实体转VO
     */
    private SemesterVO convertToVO(Semester semester) {
        if (semester == null) {
            return null;
        }

        SemesterVO semesterVO = new SemesterVO();
        BeanUtils.copyProperties(semester, semesterVO);
        return semesterVO;
    }

    /**
     * VO转实体
     */
    private void copyVOToEntity(SemesterVO semesterVO, Semester semester) {
        if (semesterVO == null || semester == null) {
            return;
        }

        BeanUtils.copyProperties(semesterVO, semester, "id", "createdAt", "updatedAt");
    }
}
