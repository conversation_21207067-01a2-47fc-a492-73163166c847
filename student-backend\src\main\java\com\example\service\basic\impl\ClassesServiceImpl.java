package com.example.service.basic.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.entity.basic.Classes;
import com.example.mapper.basic.ClassesMapper;
import com.example.service.basic.ClassesService;
import com.example.service.teacher.TeacherService;
import com.example.dto.basic.ClassesQueryDTO;
import com.example.vo.basic.ClassesVO;
import com.example.vo.educational.CourseVO;
import com.example.common.exception.BusinessException;
import lombok.RequiredArgsConstructor;

import org.springframework.beans.BeanUtils;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 班级服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-27
 */
@Service
@RequiredArgsConstructor
public class ClassesServiceImpl extends ServiceImpl<ClassesMapper, Classes> implements ClassesService {

    private final ClassesMapper classesMapper;
    private final TeacherService teacherService;

    @Override
    public IPage<ClassesVO> getClassesPage(ClassesQueryDTO query) {
        if (query == null) {
            query = new ClassesQueryDTO();
        }

        Page<ClassesVO> page = new Page<>(query.getCurrent(), query.getSize());
        return classesMapper.selectClassesPage(page, query);
    }

    @Override
    public ClassesVO getClassesById(Integer id) {
        if (id == null) {
            throw new BusinessException("班级ID不能为空");
        }

        ClassesVO classesVO = classesMapper.selectClassesById(id);
        if (classesVO == null) {
            throw new BusinessException("班级不存在");
        }

        return classesVO;
    }

    @Override
    public List<ClassesVO> getAllClasses() {
        return classesMapper.selectAllClasses();
    }

    @Override
    public List<ClassesVO> getClassesByMajorCode(String majorCode) {
        if (!StringUtils.hasText(majorCode)) {
            throw new BusinessException("专业代码不能为空");
        }

        return classesMapper.selectClassesByMajorCode(majorCode);
    }

    @Override
    public List<ClassesVO> getClassesByHeadTeacher(String headTeacherCode) {
        if (!StringUtils.hasText(headTeacherCode)) {
            throw new BusinessException("班主任工号不能为空");
        }

        return classesMapper.selectClassesByHeadTeacher(headTeacherCode);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveClasses(ClassesVO classesVO) {
        if (classesVO == null) {
            throw new BusinessException("班级信息不能为空");
        }

        // 验证班级信息
        validateClassesInfo(classesVO, null);

        try {
            Classes classes = new Classes();
            copyClassesVOToEntity(classesVO, classes);

            // 设置默认值
            classes.setStudentCount(0);
            classes.setCreatedAt(LocalDateTime.now());
            classes.setUpdatedAt(LocalDateTime.now());

            save(classes);
        } catch (Exception e) {
            throw new BusinessException("新增班级失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateClasses(ClassesVO classesVO) {
        if (classesVO == null || classesVO.getId() == null) {
            throw new BusinessException("班级信息不能为空");
        }

        // 检查班级是否存在
        Classes existingClasses = getById(classesVO.getId());
        if (existingClasses == null) {
            throw new BusinessException("班级不存在");
        }

        // 验证班级信息
        validateClassesInfo(classesVO, classesVO.getId());

        try {
            // 使用UpdateWrapper来强制更新null值
            UpdateWrapper<Classes> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("id", classesVO.getId())
                    .set("class_code", classesVO.getClassCode())
                    .set("class_name", classesVO.getClassName())
                    .set("major_code", classesVO.getMajorCode())
                    .set("grade_year", classesVO.getGradeYear())
                    .set("student_count", classesVO.getStudentCount())
                    .set("updated_at", LocalDateTime.now());

            // 处理班主任工号：空字符串转换为null
            if (StringUtils.hasText(classesVO.getHeadTeacherCode())) {
                updateWrapper.set("head_teacher_code", classesVO.getHeadTeacherCode());
            } else {
                updateWrapper.set("head_teacher_code", null);
            }

            update(updateWrapper);
        } catch (Exception e) {
            throw new BusinessException("更新班级失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteClasses(Integer id) {
        if (id == null) {
            throw new BusinessException("班级ID不能为空");
        }

        Classes classes = getById(id);
        if (classes == null) {
            throw new BusinessException("班级不存在");
        }

        // 检查是否有学生
        if (classes.getStudentCount() != null && classes.getStudentCount() > 0) {
            throw new BusinessException("该班级还有学生，无法删除");
        }

        try {
            removeById(id);
        } catch (Exception e) {
            throw new BusinessException("删除班级失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDeleteClasses(List<Integer> ids) {
        if (ids == null || ids.isEmpty()) {
            throw new BusinessException("请选择要删除的班级");
        }

        for (Integer id : ids) {
            deleteClasses(id);
        }
    }

    /**
     * 检查班级代码是否存在
     */
    private boolean isClassCodeExists(String classCode, Integer excludeId) {
        if (!StringUtils.hasText(classCode)) {
            return false;
        }

        return classesMapper.existsClassCode(classCode, excludeId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStudentCount(Integer classId, Integer studentCount) {
        if (classId == null) {
            throw new BusinessException("班级ID不能为空");
        }

        if (studentCount == null || studentCount < 0) {
            throw new BusinessException("学生人数不能为负数");
        }

        try {
            classesMapper.updateStudentCount(classId, studentCount);
        } catch (Exception e) {
            throw new BusinessException("更新班级学生人数失败");
        }
    }

    /**
     * 验证班级信息
     */
    private void validateClassesInfo(ClassesVO classesVO, Integer excludeId) {
        // 验证班级代码唯一性
        if (isClassCodeExists(classesVO.getClassCode(), excludeId)) {
            throw new BusinessException("班级代码已存在");
        }

        // 验证入学年份
        int currentYear = LocalDateTime.now().getYear();
        if (classesVO.getGradeYear() > currentYear + 1) {
            throw new BusinessException("入学年份不能超过明年");
        }

        // 验证班主任是否存在（如果提供了班主任工号）
        if (StringUtils.hasText(classesVO.getHeadTeacherCode())) {
            if (teacherService.getTeacherByCode(classesVO.getHeadTeacherCode()) == null) {
                throw new BusinessException("指定的班主任不存在");
            }
        }
    }

    /**
     * 复制ClassesVO到实体
     */
    private void copyClassesVOToEntity(ClassesVO classesVO, Classes classes) {
        BeanUtils.copyProperties(classesVO, classes);

        // 处理班主任工号：空字符串转换为null
        if (StringUtils.hasText(classesVO.getHeadTeacherCode())) {
            classes.setHeadTeacherCode(classesVO.getHeadTeacherCode());
        } else {
            classes.setHeadTeacherCode(null);
        }
    }

    @Override
    public List<CourseVO> getCoursesByClassAndSemester(String classCode, Integer semesterId) {
        if (!StringUtils.hasText(classCode)) {
            throw new BusinessException("班级代码不能为空");
        }
        if (semesterId == null) {
            throw new BusinessException("学期ID不能为空");
        }

        try {
            return classesMapper.selectCoursesByClassAndSemester(classCode, semesterId);
        } catch (Exception e) {
            throw new BusinessException("获取班级课程失败: " + e.getMessage());
        }
    }
}
