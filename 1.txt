Unchecked runtime.lastError: The message port closed before a response was received.
Unchecked runtime.lastError: The message port closed before a response was received.
Unchecked runtime.lastError: The message port closed before a response was received.
Unchecked runtime.lastError: The message port closed before a response was received.
Unchecked runtime.lastError: The message port closed before a response was received.
Unchecked runtime.lastError: The message port closed before a response was received.
Unchecked runtime.lastError: The message port closed before a response was received.
Unchecked runtime.lastError: The message port closed before a response was received.
Unchecked runtime.lastError: The message port closed before a response was received.
Unchecked runtime.lastError: The message port closed before a response was received.
Unchecked runtime.lastError: The message port closed before a response was received.
Unchecked runtime.lastError: The message port closed before a response was received.
Unchecked runtime.lastError: The message port closed before a response was received.
Unchecked runtime.lastError: The message port closed before a response was received.
Unchecked runtime.lastError: The message port closed before a response was received.
Unchecked runtime.lastError: The message port closed before a response was received.
Unchecked runtime.lastError: The message port closed before a response was received.
Unchecked runtime.lastError: The message port closed before a response was received.
Unchecked runtime.lastError: The message port closed before a response was received.
Unchecked runtime.lastError: The message port closed before a response was received.
Unchecked runtime.lastError: The message port closed before a response was received.
Unchecked runtime.lastError: The message port closed before a response was received.
Unchecked runtime.lastError: The message port closed before a response was received.
Unchecked runtime.lastError: The message port closed before a response was received.
Unchecked runtime.lastError: The message port closed before a response was received.
Unchecked runtime.lastError: The message port closed before a response was received.
Unchecked runtime.lastError: The message port closed before a response was received.
Unchecked runtime.lastError: The message port closed before a response was received.
Unchecked runtime.lastError: The message port closed before a response was received.
Unchecked runtime.lastError: The message port closed before a response was received.
Unchecked runtime.lastError: The message port closed before a response was received.
Unchecked runtime.lastError: The message port closed before a response was received.
Unchecked runtime.lastError: The message port closed before a response was received.
Unchecked runtime.lastError: The message port closed before a response was received.
Unchecked runtime.lastError: The message port closed before a response was received.
stadium.js:1 Error
    at stadium.js:1:2654
    at 297d (stadium.js:1:2794)
    at n (stadium.js:1:110)
    at 4 (stadium.js:1:2832)
    at n (stadium.js:1:110)
    at stadium.js:1:903
    at stadium.js:1:912
receive.js:620 script.js is loadded
(索引):5  [Vue warn]: Unhandled error during execution of setup function
  at <ClassList key=0 data-insp-path="C:/Users/<USER>/Desktop/vite3/vue-pure-admin-main/src/views/student/index.vue:4:5:ClassList" onViewStudents=fn<handleViewStudents> >
  at <Students data-insp-path="C:/Users/<USER>/Desktop/vite3/vue-pure-admin-main/src/layout/components/lay-content/index.vue:134:17:transitionMain" onVnodeUnmounted=fn<onVnodeUnmounted> ref=Ref< undefined >  ... >
  at <KeepAlive key=0 include= ['Student'] data-insp-path="C:/Users/<USER>/Desktop/vite3/vue-pure-admin-main/src/layout/components/lay-content/index.vue:134:17:transitionMain" >
  at <BaseTransition mode="out-in" appear=true persisted=false  ... >
  at <Transition name="fade-transform" mode="out-in" appear=true  ... >
  at <Anonymous data-insp-path="C:/Users/<USER>/Desktop/vite3/vue-pure-admin-main/src/layout/components/lay-content/index.vue:134:17:transitionMain" route= {fullPath: '/student/index', path: '/student/index', query: {…}, hash: '', name: 'Student', …} >
  at <ElScrollbar key=0 data-insp-path="C:/Users/<USER>/Desktop/vite3/vue-pure-admin-main/src/layout/components/lay-content/index.vue:111:13:el-scrollbar" wrap-style= {display: 'flex', flex-wrap: 'wrap', max-width: '100%', margin: '0 auto', transition: 'all 300ms cubic-bezier(0.4, 0, 0.2, 1)'}  ... >
  at <Index data-insp-path="C:/Users/<USER>/Desktop/vite3/vue-pure-admin-main/src/layout/components/lay-content/index.vue:109:9:LayFrame" currComp= {__v_isVNode: true, __v_skip: true, type: {…}, props: {…}, key: null, …} currRoute= {fullPath: '/student/index', path: '/student/index', query: {…}, hash: '', name: 'Student', …} >
  at <RouterView data-insp-path="C:/Users/<USER>/Desktop/vite3/vue-pure-admin-main/src/layout/components/lay-content/index.vue:107:5:router-view" >
  at <Index data-insp-path="C:/Users/<USER>/Desktop/vite3/vue-pure-admin-main/src/layout/index.vue:187:9:LayContent" fixed-header=true >
  at <Index data-insp-path="C:/Users/<USER>/Desktop/vite3/vue-pure-admin-main/src/App.vue:3:5:router-view" onVnodeUnmounted=fn<onVnodeUnmounted> ref=Ref< undefined > >
  at <RouterView data-insp-path="C:/Users/<USER>/Desktop/vite3/vue-pure-admin-main/src/App.vue:3:5:router-view" >
  at <ElConfigProvider data-insp-path="C:/Users/<USER>/Desktop/vite3/vue-pure-admin-main/src/App.vue:2:3:el-config-provider" locale= {name: 'zh-cn', el: {…}, plus: {…}} >
  at <App>
console.<computed> @ (索引):5
warn$1 @ runtime-core.esm-bundler.js:51
logError @ runtime-core.esm-bundler.js:263
handleError @ runtime-core.esm-bundler.js:255
callWithErrorHandling @ runtime-core.esm-bundler.js:201
setupStatefulComponent @ runtime-core.esm-bundler.js:7972
setupComponent @ runtime-core.esm-bundler.js:7933
mountComponent @ runtime-core.esm-bundler.js:5248
processComponent @ runtime-core.esm-bundler.js:5214
patch @ runtime-core.esm-bundler.js:4730
mountChildren @ runtime-core.esm-bundler.js:4964
mountElement @ runtime-core.esm-bundler.js:4887
processElement @ runtime-core.esm-bundler.js:4852
patch @ runtime-core.esm-bundler.js:4718
componentUpdateFn @ runtime-core.esm-bundler.js:5360
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5488
mountComponent @ runtime-core.esm-bundler.js:5262
processComponent @ runtime-core.esm-bundler.js:5214
patch @ runtime-core.esm-bundler.js:4730
componentUpdateFn @ runtime-core.esm-bundler.js:5360
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5488
mountComponent @ runtime-core.esm-bundler.js:5262
processComponent @ runtime-core.esm-bundler.js:5214
patch @ runtime-core.esm-bundler.js:4730
componentUpdateFn @ runtime-core.esm-bundler.js:5360
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5488
mountComponent @ runtime-core.esm-bundler.js:5262
processComponent @ runtime-core.esm-bundler.js:5214
patch @ runtime-core.esm-bundler.js:4730
componentUpdateFn @ runtime-core.esm-bundler.js:5360
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5488
mountComponent @ runtime-core.esm-bundler.js:5262
processComponent @ runtime-core.esm-bundler.js:5214
patch @ runtime-core.esm-bundler.js:4730
componentUpdateFn @ runtime-core.esm-bundler.js:5360
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5488
mountComponent @ runtime-core.esm-bundler.js:5262
processComponent @ runtime-core.esm-bundler.js:5214
patch @ runtime-core.esm-bundler.js:4730
mountChildren @ runtime-core.esm-bundler.js:4964
mountElement @ runtime-core.esm-bundler.js:4887
processElement @ runtime-core.esm-bundler.js:4852
patch @ runtime-core.esm-bundler.js:4718
mountChildren @ runtime-core.esm-bundler.js:4964
processFragment @ runtime-core.esm-bundler.js:5144
patch @ runtime-core.esm-bundler.js:4704
mountChildren @ runtime-core.esm-bundler.js:4964
mountElement @ runtime-core.esm-bundler.js:4887
processElement @ runtime-core.esm-bundler.js:4852
patch @ runtime-core.esm-bundler.js:4718
mountChildren @ runtime-core.esm-bundler.js:4964
mountElement @ runtime-core.esm-bundler.js:4887
processElement @ runtime-core.esm-bundler.js:4852
patch @ runtime-core.esm-bundler.js:4718
mountChildren @ runtime-core.esm-bundler.js:4964
mountElement @ runtime-core.esm-bundler.js:4887
processElement @ runtime-core.esm-bundler.js:4852
patch @ runtime-core.esm-bundler.js:4718
componentUpdateFn @ runtime-core.esm-bundler.js:5360
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5488
mountComponent @ runtime-core.esm-bundler.js:5262
processComponent @ runtime-core.esm-bundler.js:5214
patch @ runtime-core.esm-bundler.js:4730
mountChildren @ runtime-core.esm-bundler.js:4964
processFragment @ runtime-core.esm-bundler.js:5144
patch @ runtime-core.esm-bundler.js:4704
mountChildren @ runtime-core.esm-bundler.js:4964
mountElement @ runtime-core.esm-bundler.js:4887
processElement @ runtime-core.esm-bundler.js:4852
patch @ runtime-core.esm-bundler.js:4718
mountChildren @ runtime-core.esm-bundler.js:4964
processFragment @ runtime-core.esm-bundler.js:5144
patch @ runtime-core.esm-bundler.js:4704
componentUpdateFn @ runtime-core.esm-bundler.js:5360
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5488
mountComponent @ runtime-core.esm-bundler.js:5262
processComponent @ runtime-core.esm-bundler.js:5214
patch @ runtime-core.esm-bundler.js:4730
componentUpdateFn @ runtime-core.esm-bundler.js:5360
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5488
mountComponent @ runtime-core.esm-bundler.js:5262
processComponent @ runtime-core.esm-bundler.js:5214
patch @ runtime-core.esm-bundler.js:4730
mountChildren @ runtime-core.esm-bundler.js:4964
mountElement @ runtime-core.esm-bundler.js:4887
processElement @ runtime-core.esm-bundler.js:4852
patch @ runtime-core.esm-bundler.js:4718
componentUpdateFn @ runtime-core.esm-bundler.js:5360
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5488
mountComponent @ runtime-core.esm-bundler.js:5262
processComponent @ runtime-core.esm-bundler.js:5214
patch @ runtime-core.esm-bundler.js:4730
mountChildren @ runtime-core.esm-bundler.js:4964
mountElement @ runtime-core.esm-bundler.js:4887
processElement @ runtime-core.esm-bundler.js:4852
patch @ runtime-core.esm-bundler.js:4718
mountChildren @ runtime-core.esm-bundler.js:4964
mountElement @ runtime-core.esm-bundler.js:4887
processElement @ runtime-core.esm-bundler.js:4852
patch @ runtime-core.esm-bundler.js:4718
mountChildren @ runtime-core.esm-bundler.js:4964
mountElement @ runtime-core.esm-bundler.js:4887
processElement @ runtime-core.esm-bundler.js:4852
patch @ runtime-core.esm-bundler.js:4718
componentUpdateFn @ runtime-core.esm-bundler.js:5360
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5488
mountComponent @ runtime-core.esm-bundler.js:5262
processComponent @ runtime-core.esm-bundler.js:5214
patch @ runtime-core.esm-bundler.js:4730
componentUpdateFn @ runtime-core.esm-bundler.js:5440
run @ reactivity.esm-bundler.js:237
runIfDirty @ reactivity.esm-bundler.js:275
callWithErrorHandling @ runtime-core.esm-bundler.js:199
flushJobs @ runtime-core.esm-bundler.js:408
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queuePostFlushCb @ runtime-core.esm-bundler.js:336
queueEffectWithSuspense @ runtime-core.esm-bundler.js:7385
baseWatchOptions.scheduler @ runtime-core.esm-bundler.js:6261
effect2.scheduler @ reactivity.esm-bundler.js:1847
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:614
trigger @ reactivity.esm-bundler.js:588
set value @ reactivity.esm-bundler.js:1465
finalizeNavigation @ vue-router.mjs:3503
(匿名) @ vue-router.mjs:3368
Promise.then
pushWithRedirect @ vue-router.mjs:3335
push @ vue-router.mjs:3260
(匿名) @ index.ts:196
Promise.then
(匿名) @ index.ts:157
(匿名) @ vue-router.mjs:2107
runWithContext @ vue-router.mjs:2074
(匿名) @ vue-router.mjs:2107
(匿名) @ vue-router.mjs:2079
runWithContext @ runtime-core.esm-bundler.js:4024
runWithContext @ vue-router.mjs:3387
(匿名) @ vue-router.mjs:3743
Promise.then
(匿名) @ vue-router.mjs:3743
runGuardQueue @ vue-router.mjs:3743
(匿名) @ vue-router.mjs:3413
Promise.then
navigate @ vue-router.mjs:3406
pushWithRedirect @ vue-router.mjs:3327
push @ vue-router.mjs:3260
install @ vue-router.mjs:3704
use @ runtime-core.esm-bundler.js:3887
(匿名) @ main.ts:59
Promise.then
(匿名) @ main.ts:57
(索引):5  [Vue warn]: Unhandled error during execution of component update
  at <RouterView data-insp-path="C:/Users/<USER>/Desktop/vite3/vue-pure-admin-main/src/App.vue:3:5:router-view" >
  at <ElConfigProvider data-insp-path="C:/Users/<USER>/Desktop/vite3/vue-pure-admin-main/src/App.vue:2:3:el-config-provider" locale= {name: 'zh-cn', el: {…}, plus: {…}} >
  at <App>
console.<computed> @ (索引):5
warn$1 @ runtime-core.esm-bundler.js:51
logError @ runtime-core.esm-bundler.js:263
handleError @ runtime-core.esm-bundler.js:255
callWithErrorHandling @ runtime-core.esm-bundler.js:201
flushJobs @ runtime-core.esm-bundler.js:408
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queuePostFlushCb @ runtime-core.esm-bundler.js:336
queueEffectWithSuspense @ runtime-core.esm-bundler.js:7385
baseWatchOptions.scheduler @ runtime-core.esm-bundler.js:6261
effect2.scheduler @ reactivity.esm-bundler.js:1847
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:614
trigger @ reactivity.esm-bundler.js:588
set value @ reactivity.esm-bundler.js:1465
finalizeNavigation @ vue-router.mjs:3503
(匿名) @ vue-router.mjs:3368
Promise.then
pushWithRedirect @ vue-router.mjs:3335
push @ vue-router.mjs:3260
(匿名) @ index.ts:196
Promise.then
(匿名) @ index.ts:157
(匿名) @ vue-router.mjs:2107
runWithContext @ vue-router.mjs:2074
(匿名) @ vue-router.mjs:2107
(匿名) @ vue-router.mjs:2079
runWithContext @ runtime-core.esm-bundler.js:4024
runWithContext @ vue-router.mjs:3387
(匿名) @ vue-router.mjs:3743
Promise.then
(匿名) @ vue-router.mjs:3743
runGuardQueue @ vue-router.mjs:3743
(匿名) @ vue-router.mjs:3413
Promise.then
navigate @ vue-router.mjs:3406
pushWithRedirect @ vue-router.mjs:3327
push @ vue-router.mjs:3260
install @ vue-router.mjs:3704
use @ runtime-core.esm-bundler.js:3887
(匿名) @ main.ts:59
Promise.then
(匿名) @ main.ts:57
(索引):5  [Vue warn]: Unhandled error during execution of mounted hook
  at <ElBacktop data-insp-path="C:/Users/<USER>/Desktop/vite3/vue-pure-admin-main/src/layout/components/lay-content/index.vue:127:15:el-backtop" title="回到顶部" target=".app-main .el-scrollbar__wrap" >
  at <ElScrollbar key=0 data-insp-path="C:/Users/<USER>/Desktop/vite3/vue-pure-admin-main/src/layout/components/lay-content/index.vue:111:13:el-scrollbar" wrap-style= {display: 'flex', flex-wrap: 'wrap', max-width: '100%', margin: '0 auto', transition: 'all 300ms cubic-bezier(0.4, 0, 0.2, 1)'}  ... >
  at <Index data-insp-path="C:/Users/<USER>/Desktop/vite3/vue-pure-admin-main/src/layout/components/lay-content/index.vue:109:9:LayFrame" currComp= {__v_isVNode: true, __v_skip: true, type: {…}, props: {…}, key: null, …} currRoute= {fullPath: '/student/index', path: '/student/index', query: {…}, hash: '', name: 'Student', …} >
  at <RouterView data-insp-path="C:/Users/<USER>/Desktop/vite3/vue-pure-admin-main/src/layout/components/lay-content/index.vue:107:5:router-view" >
  at <Index data-insp-path="C:/Users/<USER>/Desktop/vite3/vue-pure-admin-main/src/layout/index.vue:187:9:LayContent" fixed-header=true >
  at <Index data-insp-path="C:/Users/<USER>/Desktop/vite3/vue-pure-admin-main/src/App.vue:3:5:router-view" onVnodeUnmounted=fn<onVnodeUnmounted> ref=Ref< undefined > >
  at <RouterView data-insp-path="C:/Users/<USER>/Desktop/vite3/vue-pure-admin-main/src/App.vue:3:5:router-view" >
  at <ElConfigProvider data-insp-path="C:/Users/<USER>/Desktop/vite3/vue-pure-admin-main/src/App.vue:2:3:el-config-provider" locale= {name: 'zh-cn', el: {…}, plus: {…}} >
  at <App>
console.<computed> @ (索引):5
warn$1 @ runtime-core.esm-bundler.js:51
logError @ runtime-core.esm-bundler.js:263
handleError @ runtime-core.esm-bundler.js:255
callWithErrorHandling @ runtime-core.esm-bundler.js:201
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
hook.__weh.hook.__weh @ runtime-core.esm-bundler.js:2816
flushPostFlushCbs @ runtime-core.esm-bundler.js:385
flushJobs @ runtime-core.esm-bundler.js:427
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queuePostFlushCb @ runtime-core.esm-bundler.js:336
queueEffectWithSuspense @ runtime-core.esm-bundler.js:7385
baseWatchOptions.scheduler @ runtime-core.esm-bundler.js:6261
effect2.scheduler @ reactivity.esm-bundler.js:1847
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:614
trigger @ reactivity.esm-bundler.js:588
set value @ reactivity.esm-bundler.js:1465
finalizeNavigation @ vue-router.mjs:3503
(匿名) @ vue-router.mjs:3368
Promise.then
pushWithRedirect @ vue-router.mjs:3335
push @ vue-router.mjs:3260
(匿名) @ index.ts:196
Promise.then
(匿名) @ index.ts:157
(匿名) @ vue-router.mjs:2107
runWithContext @ vue-router.mjs:2074
(匿名) @ vue-router.mjs:2107
(匿名) @ vue-router.mjs:2079
runWithContext @ runtime-core.esm-bundler.js:4024
runWithContext @ vue-router.mjs:3387
(匿名) @ vue-router.mjs:3743
Promise.then
(匿名) @ vue-router.mjs:3743
runGuardQueue @ vue-router.mjs:3743
(匿名) @ vue-router.mjs:3413
Promise.then
navigate @ vue-router.mjs:3406
pushWithRedirect @ vue-router.mjs:3327
push @ vue-router.mjs:3260
install @ vue-router.mjs:3704
use @ runtime-core.esm-bundler.js:3887
(匿名) @ main.ts:59
Promise.then
(匿名) @ main.ts:57
(索引):5  [Vue warn]: Unhandled error during execution of watcher callback
  at <ElTabBar ref=Ref< Proxy(Object) {ref: RefImpl, __v_skip: true, update: ƒ} > tabs= (3) [Proxy(Object), Proxy(Object), Proxy(Object)] tabRefs= {1: div#tab-1.el-tabs__item.is-top.is-active, 2: div#tab-2.el-tabs__item.is-top, 3: div#tab-3.el-tabs__item.is-top} >
  at <ElTabNav ref=Ref< Proxy(Object) {tabListRef: RefImpl, tabBarRef: RefImpl, scrollToActiveTab: ƒ, removeFocus: ƒ, focusActiveTab: ƒ, …} > currentName="1" editable=false  ... >
  at <IsolatedRenderer render=fn<renderFnWithContext> >
  at <Anonymous>
  at <ElTabs data-insp-path="C:/Users/<USER>/Desktop/vite3/vue-pure-admin-main/src/layout/components/lay-notice/index.vue:39:9:el-tabs" modelValue="1" onUpdate:modelValue=fn  ... >
  at <ElDropdownMenu data-insp-path="C:/Users/<USER>/Desktop/vite3/vue-pure-admin-main/src/layout/components/lay-notice/index.vue:38:7:el-dropdown-menu" >
  at <ElDropdownCollection>
  at <ElRovingFocusGroupImpl loop=true current-tab-id=null orientation="horizontal"  ... >
  at <ElRovingFocusGroupCollection loop=true current-tab-id=null orientation="horizontal"  ... >
  at <ElRovingFocusGroup loop=true current-tab-id=null orientation="horizontal"  ... >
  at <ElScrollbar ref="scrollbar" wrap-style= {maxHeight: ''} tag="div"  ... >
  at <ElFocusTrap trapped=false trap-on-focus-in=true focus-trap-el=undefined  ... >
  at <ElPopperContent key=0 id="el-id-839-9" ref_key="contentRef"  ... >
  at <BaseTransition onAfterLeave=fn<onTransitionLeave> onBeforeEnter=fn<onBeforeEnter> onAfterEnter=fn<onAfterShow>  ... >
  at <Transition name="el-zoom-in-top" onAfterLeave=fn<onTransitionLeave> onBeforeEnter=fn<onBeforeEnter>  ... >
  at <Teleport disabled=false to="#el-popper-container-839" >
  at <ElTooltipContent ref_key="contentRef" ref=Ref< Proxy(Object) {contentRef: RefImpl, __v_skip: true, isFocusInsideContent: ƒ} > aria-label=undefined  ... >
  at <ElPopper ref_key="popperRef" ref=Ref< Proxy(Object) {triggerRef: RefImpl, popperInstanceRef: RefImpl, contentRef: RefImpl, referenceRef: RefImpl, role: ComputedRefImpl, …} > role="menu"  ... >
  at <ElTooltip ref="popperRef" role="menu" effect="light"  ... >
  at <ElDropdown data-insp-path="C:/Users/<USER>/Desktop/vite3/vue-pure-admin-main/src/layout/components/lay-navbar/index.vue:58:7:LayNotice" trigger="click" placement="bottom-end"  ... >
  at <Index data-insp-path="C:/Users/<USER>/Desktop/vite3/vue-pure-admin-main/src/layout/components/lay-navbar/index.vue:58:7:LayNotice" id="header-notice" >
  at <Index>
  at <LayHeader data-insp-path="C:/Users/<USER>/Desktop/vite3/vue-pure-admin-main/src/layout/index.vue:185:9:LayHeader" >
  at <Index data-insp-path="C:/Users/<USER>/Desktop/vite3/vue-pure-admin-main/src/App.vue:3:5:router-view" onVnodeUnmounted=fn<onVnodeUnmounted> ref=Ref< undefined > >
  at <RouterView data-insp-path="C:/Users/<USER>/Desktop/vite3/vue-pure-admin-main/src/App.vue:3:5:router-view" >
  at <ElConfigProvider data-insp-path="C:/Users/<USER>/Desktop/vite3/vue-pure-admin-main/src/App.vue:2:3:el-config-provider" locale= {name: 'zh-cn', el: {…}, plus: {…}} >
  at <App>
console.<computed> @ (索引):5
warn$1 @ runtime-core.esm-bundler.js:51
logError @ runtime-core.esm-bundler.js:263
handleError @ runtime-core.esm-bundler.js:255
(匿名) @ runtime-core.esm-bundler.js:209
Promise.catch
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:208
baseWatchOptions.call @ runtime-core.esm-bundler.js:6257
job @ reactivity.esm-bundler.js:1831
watch @ reactivity.esm-bundler.js:1866
doWatch @ runtime-core.esm-bundler.js:6285
watch2 @ runtime-core.esm-bundler.js:6218
setup @ tab-bar.vue:87
callWithErrorHandling @ runtime-core.esm-bundler.js:199
setupStatefulComponent @ runtime-core.esm-bundler.js:7972
setupComponent @ runtime-core.esm-bundler.js:7933
mountComponent @ runtime-core.esm-bundler.js:5248
processComponent @ runtime-core.esm-bundler.js:5214
patch @ runtime-core.esm-bundler.js:4730
mountChildren @ runtime-core.esm-bundler.js:4964
mountElement @ runtime-core.esm-bundler.js:4887
processElement @ runtime-core.esm-bundler.js:4852
patch @ runtime-core.esm-bundler.js:4718
mountChildren @ runtime-core.esm-bundler.js:4964
mountElement @ runtime-core.esm-bundler.js:4887
processElement @ runtime-core.esm-bundler.js:4852
patch @ runtime-core.esm-bundler.js:4718
mountChildren @ runtime-core.esm-bundler.js:4964
mountElement @ runtime-core.esm-bundler.js:4887
processElement @ runtime-core.esm-bundler.js:4852
patch @ runtime-core.esm-bundler.js:4718
componentUpdateFn @ runtime-core.esm-bundler.js:5360
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5488
mountComponent @ runtime-core.esm-bundler.js:5262
processComponent @ runtime-core.esm-bundler.js:5214
patch @ runtime-core.esm-bundler.js:4730
mountChildren @ runtime-core.esm-bundler.js:4964
processFragment @ runtime-core.esm-bundler.js:5144
patch @ runtime-core.esm-bundler.js:4704
componentUpdateFn @ runtime-core.esm-bundler.js:5360
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5488
mountComponent @ runtime-core.esm-bundler.js:5262
processComponent @ runtime-core.esm-bundler.js:5214
patch @ runtime-core.esm-bundler.js:4730
componentUpdateFn @ runtime-core.esm-bundler.js:5360
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5488
mountComponent @ runtime-core.esm-bundler.js:5262
processComponent @ runtime-core.esm-bundler.js:5214
patch @ runtime-core.esm-bundler.js:4730
mountChildren @ runtime-core.esm-bundler.js:4964
mountElement @ runtime-core.esm-bundler.js:4887
processElement @ runtime-core.esm-bundler.js:4852
patch @ runtime-core.esm-bundler.js:4718
mountChildren @ runtime-core.esm-bundler.js:4964
mountElement @ runtime-core.esm-bundler.js:4887
processElement @ runtime-core.esm-bundler.js:4852
patch @ runtime-core.esm-bundler.js:4718
componentUpdateFn @ runtime-core.esm-bundler.js:5360
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5488
mountComponent @ runtime-core.esm-bundler.js:5262
processComponent @ runtime-core.esm-bundler.js:5214
patch @ runtime-core.esm-bundler.js:4730
mountChildren @ runtime-core.esm-bundler.js:4964
processFragment @ runtime-core.esm-bundler.js:5144
patch @ runtime-core.esm-bundler.js:4704
mountChildren @ runtime-core.esm-bundler.js:4964
mountElement @ runtime-core.esm-bundler.js:4887
processElement @ runtime-core.esm-bundler.js:4852
patch @ runtime-core.esm-bundler.js:4718
componentUpdateFn @ runtime-core.esm-bundler.js:5360
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5488
mountComponent @ runtime-core.esm-bundler.js:5262
processComponent @ runtime-core.esm-bundler.js:5214
patch @ runtime-core.esm-bundler.js:4730
mountChildren @ runtime-core.esm-bundler.js:4964
processFragment @ runtime-core.esm-bundler.js:5144
patch @ runtime-core.esm-bundler.js:4704
mountChildren @ runtime-core.esm-bundler.js:4964
processFragment @ runtime-core.esm-bundler.js:5144
patch @ runtime-core.esm-bundler.js:4704
componentUpdateFn @ runtime-core.esm-bundler.js:5360
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5488
mountComponent @ runtime-core.esm-bundler.js:5262
processComponent @ runtime-core.esm-bundler.js:5214
patch @ runtime-core.esm-bundler.js:4730
mountChildren @ runtime-core.esm-bundler.js:4964
processFragment @ runtime-core.esm-bundler.js:5144
patch @ runtime-core.esm-bundler.js:4704
mountChildren @ runtime-core.esm-bundler.js:4964
processFragment @ runtime-core.esm-bundler.js:5144
patch @ runtime-core.esm-bundler.js:4704
componentUpdateFn @ runtime-core.esm-bundler.js:5360
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5488
mountComponent @ runtime-core.esm-bundler.js:5262
processComponent @ runtime-core.esm-bundler.js:5214
patch @ runtime-core.esm-bundler.js:4730
mountChildren @ runtime-core.esm-bundler.js:4964
processFragment @ runtime-core.esm-bundler.js:5144
patch @ runtime-core.esm-bundler.js:4704
componentUpdateFn @ runtime-core.esm-bundler.js:5360
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5488
mountComponent @ runtime-core.esm-bundler.js:5262
processComponent @ runtime-core.esm-bundler.js:5214
patch @ runtime-core.esm-bundler.js:4730
componentUpdateFn @ runtime-core.esm-bundler.js:5360
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5488
mountComponent @ runtime-core.esm-bundler.js:5262
processComponent @ runtime-core.esm-bundler.js:5214
patch @ runtime-core.esm-bundler.js:4730
mountChildren @ runtime-core.esm-bundler.js:4964
processFragment @ runtime-core.esm-bundler.js:5144
patch @ runtime-core.esm-bundler.js:4704
mountChildren @ runtime-core.esm-bundler.js:4964
mountElement @ runtime-core.esm-bundler.js:4887
processElement @ runtime-core.esm-bundler.js:4852
patch @ runtime-core.esm-bundler.js:4718
mountChildren @ runtime-core.esm-bundler.js:4964
mountElement @ runtime-core.esm-bundler.js:4887
processElement @ runtime-core.esm-bundler.js:4852
patch @ runtime-core.esm-bundler.js:4718
mountChildren @ runtime-core.esm-bundler.js:4964
mountElement @ runtime-core.esm-bundler.js:4887
processElement @ runtime-core.esm-bundler.js:4852
patch @ runtime-core.esm-bundler.js:4718
componentUpdateFn @ runtime-core.esm-bundler.js:5360
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5488
mountComponent @ runtime-core.esm-bundler.js:5262
processComponent @ runtime-core.esm-bundler.js:5214
patch @ runtime-core.esm-bundler.js:4730
mountChildren @ runtime-core.esm-bundler.js:4964
processFragment @ runtime-core.esm-bundler.js:5144
patch @ runtime-core.esm-bundler.js:4704
mountChildren @ runtime-core.esm-bundler.js:4964
processFragment @ runtime-core.esm-bundler.js:5144
patch @ runtime-core.esm-bundler.js:4704
mountChildren @ runtime-core.esm-bundler.js:4964
processFragment @ runtime-core.esm-bundler.js:5144
patch @ runtime-core.esm-bundler.js:4704
mountChildren @ runtime-core.esm-bundler.js:4964
processFragment @ runtime-core.esm-bundler.js:5144
patch @ runtime-core.esm-bundler.js:4704
componentUpdateFn @ runtime-core.esm-bundler.js:5360
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5488
mountComponent @ runtime-core.esm-bundler.js:5262
processComponent @ runtime-core.esm-bundler.js:5214
patch @ runtime-core.esm-bundler.js:4730
mountChildren @ runtime-core.esm-bundler.js:4964
mountElement @ runtime-core.esm-bundler.js:4887
processElement @ runtime-core.esm-bundler.js:4852
patch @ runtime-core.esm-bundler.js:4718
componentUpdateFn @ runtime-core.esm-bundler.js:5360
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5488
mountComponent @ runtime-core.esm-bundler.js:5262
processComponent @ runtime-core.esm-bundler.js:5214
patch @ runtime-core.esm-bundler.js:4730
componentUpdateFn @ runtime-core.esm-bundler.js:5360
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5488
mountComponent @ runtime-core.esm-bundler.js:5262
processComponent @ runtime-core.esm-bundler.js:5214
patch @ runtime-core.esm-bundler.js:4730
componentUpdateFn @ runtime-core.esm-bundler.js:5360
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5488
mountComponent @ runtime-core.esm-bundler.js:5262
processComponent @ runtime-core.esm-bundler.js:5214
patch @ runtime-core.esm-bundler.js:4730
mountChildren @ runtime-core.esm-bundler.js:4964
processFragment @ runtime-core.esm-bundler.js:5144
patch @ runtime-core.esm-bundler.js:4704
mountChildren @ runtime-core.esm-bundler.js:4964
mount @ runtime-core.esm-bundler.js:824
mountToTarget @ runtime-core.esm-bundler.js:846
process @ runtime-core.esm-bundler.js:868
patch @ runtime-core.esm-bundler.js:4742
componentUpdateFn @ runtime-core.esm-bundler.js:5360
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5488
mountComponent @ runtime-core.esm-bundler.js:5262
processComponent @ runtime-core.esm-bundler.js:5214
patch @ runtime-core.esm-bundler.js:4730
componentUpdateFn @ runtime-core.esm-bundler.js:5360
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5488
mountComponent @ runtime-core.esm-bundler.js:5262
processComponent @ runtime-core.esm-bundler.js:5214
patch @ runtime-core.esm-bundler.js:4730
mountChildren @ runtime-core.esm-bundler.js:4964
processFragment @ runtime-core.esm-bundler.js:5144
patch @ runtime-core.esm-bundler.js:4704
componentUpdateFn @ runtime-core.esm-bundler.js:5360
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5488
mountComponent @ runtime-core.esm-bundler.js:5262
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queuePostFlushCb @ runtime-core.esm-bundler.js:336
queueEffectWithSuspense @ runtime-core.esm-bundler.js:7385
baseWatchOptions.scheduler @ runtime-core.esm-bundler.js:6261
effect2.scheduler @ reactivity.esm-bundler.js:1847
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:614
trigger @ reactivity.esm-bundler.js:588
set value @ reactivity.esm-bundler.js:1465
finalizeNavigation @ vue-router.mjs:3503
(匿名) @ vue-router.mjs:3368
Promise.then
pushWithRedirect @ vue-router.mjs:3335
push @ vue-router.mjs:3260
(匿名) @ index.ts:196
Promise.then
(匿名) @ index.ts:157
(匿名) @ vue-router.mjs:2107
runWithContext @ vue-router.mjs:2074
(匿名) @ vue-router.mjs:2107
(匿名) @ vue-router.mjs:2079
runWithContext @ runtime-core.esm-bundler.js:4024
runWithContext @ vue-router.mjs:3387
(匿名) @ vue-router.mjs:3743
Promise.then
(匿名) @ vue-router.mjs:3743
runGuardQueue @ vue-router.mjs:3743
(匿名) @ vue-router.mjs:3413
Promise.then
navigate @ vue-router.mjs:3406
pushWithRedirect @ vue-router.mjs:3327
push @ vue-router.mjs:3260
install @ vue-router.mjs:3704
use @ runtime-core.esm-bundler.js:3887
(匿名) @ main.ts:59
Promise.then
(匿名) @ main.ts:57
(索引):5  [Vue Router warn]: uncaught error during route navigation:
console.<computed> @ (索引):5
warn @ vue-router.mjs:51
triggerError @ vue-router.mjs:3621
(匿名) @ vue-router.mjs:3660
Promise.catch
handleScroll @ vue-router.mjs:3660
finalizeNavigation @ vue-router.mjs:3504
(匿名) @ vue-router.mjs:3368
Promise.then
pushWithRedirect @ vue-router.mjs:3335
push @ vue-router.mjs:3260
(匿名) @ index.ts:196
Promise.then
(匿名) @ index.ts:157
(匿名) @ vue-router.mjs:2107
runWithContext @ vue-router.mjs:2074
(匿名) @ vue-router.mjs:2107
(匿名) @ vue-router.mjs:2079
runWithContext @ runtime-core.esm-bundler.js:4024
runWithContext @ vue-router.mjs:3387
(匿名) @ vue-router.mjs:3743
Promise.then
(匿名) @ vue-router.mjs:3743
runGuardQueue @ vue-router.mjs:3743
(匿名) @ vue-router.mjs:3413
Promise.then
navigate @ vue-router.mjs:3406
pushWithRedirect @ vue-router.mjs:3327
push @ vue-router.mjs:3260
install @ vue-router.mjs:3704
use @ runtime-core.esm-bundler.js:3887
(匿名) @ main.ts:59
Promise.then
(匿名) @ main.ts:57
(索引):5  ElementPlusError: [ElBacktop] target does not exist: .app-main .el-scrollbar__wrap
    at throwError (error.ts:9:9)
    at use-backtop.ts:26:9
    at runtime-core.esm-bundler.js:2836:40
    at callWithErrorHandling (runtime-core.esm-bundler.js:199:19)
    at callWithAsyncErrorHandling (runtime-core.esm-bundler.js:206:17)
    at hook.__weh.hook.__weh (runtime-core.esm-bundler.js:2816:19)
    at flushPostFlushCbs (runtime-core.esm-bundler.js:385:28)
    at flushJobs (runtime-core.esm-bundler.js:427:5)
console.<computed> @ (索引):5
triggerError @ vue-router.mjs:3623
(匿名) @ vue-router.mjs:3660
Promise.catch
handleScroll @ vue-router.mjs:3660
finalizeNavigation @ vue-router.mjs:3504
(匿名) @ vue-router.mjs:3368
Promise.then
pushWithRedirect @ vue-router.mjs:3335
push @ vue-router.mjs:3260
(匿名) @ index.ts:196
Promise.then
(匿名) @ index.ts:157
(匿名) @ vue-router.mjs:2107
runWithContext @ vue-router.mjs:2074
(匿名) @ vue-router.mjs:2107
(匿名) @ vue-router.mjs:2079
runWithContext @ runtime-core.esm-bundler.js:4024
runWithContext @ vue-router.mjs:3387
(匿名) @ vue-router.mjs:3743
Promise.then
(匿名) @ vue-router.mjs:3743
runGuardQueue @ vue-router.mjs:3743
(匿名) @ vue-router.mjs:3413
Promise.then
navigate @ vue-router.mjs:3406
pushWithRedirect @ vue-router.mjs:3327
push @ vue-router.mjs:3260
install @ vue-router.mjs:3704
use @ runtime-core.esm-bundler.js:3887
(匿名) @ main.ts:59
Promise.then
(匿名) @ main.ts:57
error.ts:9  Uncaught (in promise) ElementPlusError: [ElBacktop] target does not exist: .app-main .el-scrollbar__wrap
    at throwError (error.ts:9:9)
    at use-backtop.ts:26:9
    at runtime-core.esm-bundler.js:2836:40
    at callWithErrorHandling (runtime-core.esm-bundler.js:199:19)
    at callWithAsyncErrorHandling (runtime-core.esm-bundler.js:206:17)
    at hook.__weh.hook.__weh (runtime-core.esm-bundler.js:2816:19)
    at flushPostFlushCbs (runtime-core.esm-bundler.js:385:28)
    at flushJobs (runtime-core.esm-bundler.js:427:5)
throwError @ error.ts:9
(匿名) @ use-backtop.ts:26
(匿名) @ runtime-core.esm-bundler.js:2836
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
hook.__weh.hook.__weh @ runtime-core.esm-bundler.js:2816
flushPostFlushCbs @ runtime-core.esm-bundler.js:385
flushJobs @ runtime-core.esm-bundler.js:427
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queuePostFlushCb @ runtime-core.esm-bundler.js:336
queueEffectWithSuspense @ runtime-core.esm-bundler.js:7385
baseWatchOptions.scheduler @ runtime-core.esm-bundler.js:6261
effect2.scheduler @ reactivity.esm-bundler.js:1847
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:614
trigger @ reactivity.esm-bundler.js:588
set value @ reactivity.esm-bundler.js:1465
finalizeNavigation @ vue-router.mjs:3503
(匿名) @ vue-router.mjs:3368
Promise.then
pushWithRedirect @ vue-router.mjs:3335
push @ vue-router.mjs:3260
(匿名) @ index.ts:196
Promise.then
(匿名) @ index.ts:157
(匿名) @ vue-router.mjs:2107
runWithContext @ vue-router.mjs:2074
(匿名) @ vue-router.mjs:2107
(匿名) @ vue-router.mjs:2079
runWithContext @ runtime-core.esm-bundler.js:4024
runWithContext @ vue-router.mjs:3387
(匿名) @ vue-router.mjs:3743
Promise.then
(匿名) @ vue-router.mjs:3743
runGuardQueue @ vue-router.mjs:3743
(匿名) @ vue-router.mjs:3413
Promise.then
navigate @ vue-router.mjs:3406
pushWithRedirect @ vue-router.mjs:3327
push @ vue-router.mjs:3260
install @ vue-router.mjs:3704
use @ runtime-core.esm-bundler.js:3887
(匿名) @ main.ts:59
Promise.then
(匿名) @ main.ts:57
error.ts:9  Uncaught (in promise) ElementPlusError: [ElBacktop] target does not exist: .app-main .el-scrollbar__wrap
    at throwError (error.ts:9:9)
    at use-backtop.ts:26:9
    at runtime-core.esm-bundler.js:2836:40
    at callWithErrorHandling (runtime-core.esm-bundler.js:199:19)
    at callWithAsyncErrorHandling (runtime-core.esm-bundler.js:206:17)
    at hook.__weh.hook.__weh (runtime-core.esm-bundler.js:2816:19)
    at flushPostFlushCbs (runtime-core.esm-bundler.js:385:28)
    at flushJobs (runtime-core.esm-bundler.js:427:5)
throwError @ error.ts:9
(匿名) @ use-backtop.ts:26
(匿名) @ runtime-core.esm-bundler.js:2836
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
hook.__weh.hook.__weh @ runtime-core.esm-bundler.js:2816
flushPostFlushCbs @ runtime-core.esm-bundler.js:385
flushJobs @ runtime-core.esm-bundler.js:427
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queuePostFlushCb @ runtime-core.esm-bundler.js:336
queueEffectWithSuspense @ runtime-core.esm-bundler.js:7385
baseWatchOptions.scheduler @ runtime-core.esm-bundler.js:6261
effect2.scheduler @ reactivity.esm-bundler.js:1847
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:614
trigger @ reactivity.esm-bundler.js:588
set value @ reactivity.esm-bundler.js:1465
finalizeNavigation @ vue-router.mjs:3503
(匿名) @ vue-router.mjs:3368
Promise.then
pushWithRedirect @ vue-router.mjs:3335
push @ vue-router.mjs:3260
(匿名) @ index.ts:196
Promise.then
(匿名) @ index.ts:157
(匿名) @ vue-router.mjs:2107
runWithContext @ vue-router.mjs:2074
(匿名) @ vue-router.mjs:2107
(匿名) @ vue-router.mjs:2079
runWithContext @ runtime-core.esm-bundler.js:4024
runWithContext @ vue-router.mjs:3387
(匿名) @ vue-router.mjs:3743
Promise.then
(匿名) @ vue-router.mjs:3743
runGuardQueue @ vue-router.mjs:3743
(匿名) @ vue-router.mjs:3413
Promise.then
navigate @ vue-router.mjs:3406
pushWithRedirect @ vue-router.mjs:3327
push @ vue-router.mjs:3260
install @ vue-router.mjs:3704
use @ runtime-core.esm-bundler.js:3887
(匿名) @ main.ts:59
Promise.then
(匿名) @ main.ts:57
error.ts:9  Uncaught (in promise) ElementPlusError: [ElBacktop] target does not exist: .app-main .el-scrollbar__wrap
    at throwError (error.ts:9:9)
    at use-backtop.ts:26:9
    at runtime-core.esm-bundler.js:2836:40
    at callWithErrorHandling (runtime-core.esm-bundler.js:199:19)
    at callWithAsyncErrorHandling (runtime-core.esm-bundler.js:206:17)
    at hook.__weh.hook.__weh (runtime-core.esm-bundler.js:2816:19)
    at flushPostFlushCbs (runtime-core.esm-bundler.js:385:28)
    at flushJobs (runtime-core.esm-bundler.js:427:5)
throwError @ error.ts:9
(匿名) @ use-backtop.ts:26
(匿名) @ runtime-core.esm-bundler.js:2836
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
hook.__weh.hook.__weh @ runtime-core.esm-bundler.js:2816
flushPostFlushCbs @ runtime-core.esm-bundler.js:385
flushJobs @ runtime-core.esm-bundler.js:427
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queuePostFlushCb @ runtime-core.esm-bundler.js:336
queueEffectWithSuspense @ runtime-core.esm-bundler.js:7385
baseWatchOptions.scheduler @ runtime-core.esm-bundler.js:6261
effect2.scheduler @ reactivity.esm-bundler.js:1847
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:614
trigger @ reactivity.esm-bundler.js:588
set value @ reactivity.esm-bundler.js:1465
finalizeNavigation @ vue-router.mjs:3503
(匿名) @ vue-router.mjs:3368
Promise.then
pushWithRedirect @ vue-router.mjs:3335
push @ vue-router.mjs:3260
(匿名) @ index.ts:196
Promise.then
(匿名) @ index.ts:157
(匿名) @ vue-router.mjs:2107
runWithContext @ vue-router.mjs:2074
(匿名) @ vue-router.mjs:2107
(匿名) @ vue-router.mjs:2079
runWithContext @ runtime-core.esm-bundler.js:4024
runWithContext @ vue-router.mjs:3387
(匿名) @ vue-router.mjs:3743
Promise.then
(匿名) @ vue-router.mjs:3743
runGuardQueue @ vue-router.mjs:3743
(匿名) @ vue-router.mjs:3413
Promise.then
navigate @ vue-router.mjs:3406
pushWithRedirect @ vue-router.mjs:3327
push @ vue-router.mjs:3260
install @ vue-router.mjs:3704
use @ runtime-core.esm-bundler.js:3887
(匿名) @ main.ts:59
Promise.then
(匿名) @ main.ts:57
error.ts:9  Uncaught (in promise) ElementPlusError: [ElBacktop] target does not exist: .app-main .el-scrollbar__wrap
    at throwError (error.ts:9:9)
    at use-backtop.ts:26:9
    at runtime-core.esm-bundler.js:2836:40
    at callWithErrorHandling (runtime-core.esm-bundler.js:199:19)
    at callWithAsyncErrorHandling (runtime-core.esm-bundler.js:206:17)
    at hook.__weh.hook.__weh (runtime-core.esm-bundler.js:2816:19)
    at flushPostFlushCbs (runtime-core.esm-bundler.js:385:28)
    at flushJobs (runtime-core.esm-bundler.js:427:5)
throwError @ error.ts:9
(匿名) @ use-backtop.ts:26
(匿名) @ runtime-core.esm-bundler.js:2836
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
hook.__weh.hook.__weh @ runtime-core.esm-bundler.js:2816
flushPostFlushCbs @ runtime-core.esm-bundler.js:385
flushJobs @ runtime-core.esm-bundler.js:427
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queuePostFlushCb @ runtime-core.esm-bundler.js:336
queueEffectWithSuspense @ runtime-core.esm-bundler.js:7385
baseWatchOptions.scheduler @ runtime-core.esm-bundler.js:6261
effect2.scheduler @ reactivity.esm-bundler.js:1847
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:614
trigger @ reactivity.esm-bundler.js:588
set value @ reactivity.esm-bundler.js:1465
finalizeNavigation @ vue-router.mjs:3503
(匿名) @ vue-router.mjs:3368
Promise.then
pushWithRedirect @ vue-router.mjs:3335
push @ vue-router.mjs:3260
(匿名) @ index.ts:196
Promise.then
(匿名) @ index.ts:157
(匿名) @ vue-router.mjs:2107
runWithContext @ vue-router.mjs:2074
(匿名) @ vue-router.mjs:2107
(匿名) @ vue-router.mjs:2079
runWithContext @ runtime-core.esm-bundler.js:4024
runWithContext @ vue-router.mjs:3387
(匿名) @ vue-router.mjs:3743
Promise.then
(匿名) @ vue-router.mjs:3743
runGuardQueue @ vue-router.mjs:3743
(匿名) @ vue-router.mjs:3413
Promise.then
navigate @ vue-router.mjs:3406
pushWithRedirect @ vue-router.mjs:3327
push @ vue-router.mjs:3260
install @ vue-router.mjs:3704
use @ runtime-core.esm-bundler.js:3887
(匿名) @ main.ts:59
Promise.then
(匿名) @ main.ts:57
error.ts:9  Uncaught (in promise) ElementPlusError: [ElBacktop] target does not exist: .app-main .el-scrollbar__wrap
    at throwError (error.ts:9:9)
    at use-backtop.ts:26:9
    at runtime-core.esm-bundler.js:2836:40
    at callWithErrorHandling (runtime-core.esm-bundler.js:199:19)
    at callWithAsyncErrorHandling (runtime-core.esm-bundler.js:206:17)
    at hook.__weh.hook.__weh (runtime-core.esm-bundler.js:2816:19)
    at flushPostFlushCbs (runtime-core.esm-bundler.js:385:28)
    at flushJobs (runtime-core.esm-bundler.js:427:5)
throwError @ error.ts:9
(匿名) @ use-backtop.ts:26
(匿名) @ runtime-core.esm-bundler.js:2836
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
hook.__weh.hook.__weh @ runtime-core.esm-bundler.js:2816
flushPostFlushCbs @ runtime-core.esm-bundler.js:385
flushJobs @ runtime-core.esm-bundler.js:427
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queuePostFlushCb @ runtime-core.esm-bundler.js:336
queueEffectWithSuspense @ runtime-core.esm-bundler.js:7385
baseWatchOptions.scheduler @ runtime-core.esm-bundler.js:6261
effect2.scheduler @ reactivity.esm-bundler.js:1847
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:614
trigger @ reactivity.esm-bundler.js:588
set value @ reactivity.esm-bundler.js:1465
finalizeNavigation @ vue-router.mjs:3503
(匿名) @ vue-router.mjs:3368
Promise.then
pushWithRedirect @ vue-router.mjs:3335
push @ vue-router.mjs:3260
(匿名) @ index.ts:196
Promise.then
(匿名) @ index.ts:157
(匿名) @ vue-router.mjs:2107
runWithContext @ vue-router.mjs:2074
(匿名) @ vue-router.mjs:2107
(匿名) @ vue-router.mjs:2079
runWithContext @ runtime-core.esm-bundler.js:4024
runWithContext @ vue-router.mjs:3387
(匿名) @ vue-router.mjs:3743
Promise.then
(匿名) @ vue-router.mjs:3743
runGuardQueue @ vue-router.mjs:3743
(匿名) @ vue-router.mjs:3413
Promise.then
navigate @ vue-router.mjs:3406
pushWithRedirect @ vue-router.mjs:3327
push @ vue-router.mjs:3260
install @ vue-router.mjs:3704
use @ runtime-core.esm-bundler.js:3887
(匿名) @ main.ts:59
Promise.then
(匿名) @ main.ts:57
error.ts:9  Uncaught (in promise) ElementPlusError: [ElBacktop] target does not exist: .app-main .el-scrollbar__wrap
    at throwError (error.ts:9:9)
    at use-backtop.ts:26:9
    at runtime-core.esm-bundler.js:2836:40
    at callWithErrorHandling (runtime-core.esm-bundler.js:199:19)
    at callWithAsyncErrorHandling (runtime-core.esm-bundler.js:206:17)
    at hook.__weh.hook.__weh (runtime-core.esm-bundler.js:2816:19)
    at flushPostFlushCbs (runtime-core.esm-bundler.js:385:28)
    at flushJobs (runtime-core.esm-bundler.js:427:5)
throwError @ error.ts:9
(匿名) @ use-backtop.ts:26
(匿名) @ runtime-core.esm-bundler.js:2836
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
hook.__weh.hook.__weh @ runtime-core.esm-bundler.js:2816
flushPostFlushCbs @ runtime-core.esm-bundler.js:385
flushJobs @ runtime-core.esm-bundler.js:427
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queuePostFlushCb @ runtime-core.esm-bundler.js:336
queueEffectWithSuspense @ runtime-core.esm-bundler.js:7385
baseWatchOptions.scheduler @ runtime-core.esm-bundler.js:6261
effect2.scheduler @ reactivity.esm-bundler.js:1847
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:614
trigger @ reactivity.esm-bundler.js:588
set value @ reactivity.esm-bundler.js:1465
finalizeNavigation @ vue-router.mjs:3503
(匿名) @ vue-router.mjs:3368
Promise.then
pushWithRedirect @ vue-router.mjs:3335
push @ vue-router.mjs:3260
(匿名) @ index.ts:196
Promise.then
(匿名) @ index.ts:157
(匿名) @ vue-router.mjs:2107
runWithContext @ vue-router.mjs:2074
(匿名) @ vue-router.mjs:2107
(匿名) @ vue-router.mjs:2079
runWithContext @ runtime-core.esm-bundler.js:4024
runWithContext @ vue-router.mjs:3387
(匿名) @ vue-router.mjs:3743
Promise.then
(匿名) @ vue-router.mjs:3743
runGuardQueue @ vue-router.mjs:3743
(匿名) @ vue-router.mjs:3413
Promise.then
navigate @ vue-router.mjs:3406
pushWithRedirect @ vue-router.mjs:3327
push @ vue-router.mjs:3260
install @ vue-router.mjs:3704
use @ runtime-core.esm-bundler.js:3887
(匿名) @ main.ts:59
Promise.then
(匿名) @ main.ts:57
error.ts:9  Uncaught (in promise) ElementPlusError: [ElBacktop] target does not exist: .app-main .el-scrollbar__wrap
    at throwError (error.ts:9:9)
    at use-backtop.ts:26:9
    at runtime-core.esm-bundler.js:2836:40
    at callWithErrorHandling (runtime-core.esm-bundler.js:199:19)
    at callWithAsyncErrorHandling (runtime-core.esm-bundler.js:206:17)
    at hook.__weh.hook.__weh (runtime-core.esm-bundler.js:2816:19)
    at flushPostFlushCbs (runtime-core.esm-bundler.js:385:28)
    at flushJobs (runtime-core.esm-bundler.js:427:5)
throwError @ error.ts:9
(匿名) @ use-backtop.ts:26
(匿名) @ runtime-core.esm-bundler.js:2836
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
hook.__weh.hook.__weh @ runtime-core.esm-bundler.js:2816
flushPostFlushCbs @ runtime-core.esm-bundler.js:385
flushJobs @ runtime-core.esm-bundler.js:427
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queuePostFlushCb @ runtime-core.esm-bundler.js:336
queueEffectWithSuspense @ runtime-core.esm-bundler.js:7385
baseWatchOptions.scheduler @ runtime-core.esm-bundler.js:6261
effect2.scheduler @ reactivity.esm-bundler.js:1847
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:614
trigger @ reactivity.esm-bundler.js:588
set value @ reactivity.esm-bundler.js:1465
finalizeNavigation @ vue-router.mjs:3503
(匿名) @ vue-router.mjs:3368
Promise.then
pushWithRedirect @ vue-router.mjs:3335
push @ vue-router.mjs:3260
(匿名) @ index.ts:196
Promise.then
(匿名) @ index.ts:157
(匿名) @ vue-router.mjs:2107
runWithContext @ vue-router.mjs:2074
(匿名) @ vue-router.mjs:2107
(匿名) @ vue-router.mjs:2079
runWithContext @ runtime-core.esm-bundler.js:4024
runWithContext @ vue-router.mjs:3387
(匿名) @ vue-router.mjs:3743
Promise.then
(匿名) @ vue-router.mjs:3743
runGuardQueue @ vue-router.mjs:3743
(匿名) @ vue-router.mjs:3413
Promise.then
navigate @ vue-router.mjs:3406
pushWithRedirect @ vue-router.mjs:3327
push @ vue-router.mjs:3260
install @ vue-router.mjs:3704
use @ runtime-core.esm-bundler.js:3887
(匿名) @ main.ts:59
Promise.then
(匿名) @ main.ts:57
error.ts:9  Uncaught (in promise) ElementPlusError: [ElBacktop] target does not exist: .app-main .el-scrollbar__wrap
    at throwError (error.ts:9:9)
    at use-backtop.ts:26:9
    at runtime-core.esm-bundler.js:2836:40
    at callWithErrorHandling (runtime-core.esm-bundler.js:199:19)
    at callWithAsyncErrorHandling (runtime-core.esm-bundler.js:206:17)
    at hook.__weh.hook.__weh (runtime-core.esm-bundler.js:2816:19)
    at flushPostFlushCbs (runtime-core.esm-bundler.js:385:28)
    at flushJobs (runtime-core.esm-bundler.js:427:5)
throwError @ error.ts:9
(匿名) @ use-backtop.ts:26
(匿名) @ runtime-core.esm-bundler.js:2836
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
hook.__weh.hook.__weh @ runtime-core.esm-bundler.js:2816
flushPostFlushCbs @ runtime-core.esm-bundler.js:385
flushJobs @ runtime-core.esm-bundler.js:427
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queuePostFlushCb @ runtime-core.esm-bundler.js:336
queueEffectWithSuspense @ runtime-core.esm-bundler.js:7385
baseWatchOptions.scheduler @ runtime-core.esm-bundler.js:6261
effect2.scheduler @ reactivity.esm-bundler.js:1847
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:614
trigger @ reactivity.esm-bundler.js:588
set value @ reactivity.esm-bundler.js:1465
finalizeNavigation @ vue-router.mjs:3503
(匿名) @ vue-router.mjs:3368
Promise.then
pushWithRedirect @ vue-router.mjs:3335
push @ vue-router.mjs:3260
(匿名) @ index.ts:196
Promise.then
(匿名) @ index.ts:157
(匿名) @ vue-router.mjs:2107
runWithContext @ vue-router.mjs:2074
(匿名) @ vue-router.mjs:2107
(匿名) @ vue-router.mjs:2079
runWithContext @ runtime-core.esm-bundler.js:4024
runWithContext @ vue-router.mjs:3387
(匿名) @ vue-router.mjs:3743
Promise.then
(匿名) @ vue-router.mjs:3743
runGuardQueue @ vue-router.mjs:3743
(匿名) @ vue-router.mjs:3413
Promise.then
navigate @ vue-router.mjs:3406
pushWithRedirect @ vue-router.mjs:3327
push @ vue-router.mjs:3260
install @ vue-router.mjs:3704
use @ runtime-core.esm-bundler.js:3887
(匿名) @ main.ts:59
Promise.then
(匿名) @ main.ts:57
error.ts:9  Uncaught (in promise) ElementPlusError: [ElBacktop] target does not exist: .app-main .el-scrollbar__wrap
    at throwError (error.ts:9:9)
    at use-backtop.ts:26:9
    at runtime-core.esm-bundler.js:2836:40
    at callWithErrorHandling (runtime-core.esm-bundler.js:199:19)
    at callWithAsyncErrorHandling (runtime-core.esm-bundler.js:206:17)
    at hook.__weh.hook.__weh (runtime-core.esm-bundler.js:2816:19)
    at flushPostFlushCbs (runtime-core.esm-bundler.js:385:28)
    at flushJobs (runtime-core.esm-bundler.js:427:5)
throwError @ error.ts:9
(匿名) @ use-backtop.ts:26
(匿名) @ runtime-core.esm-bundler.js:2836
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
hook.__weh.hook.__weh @ runtime-core.esm-bundler.js:2816
flushPostFlushCbs @ runtime-core.esm-bundler.js:385
flushJobs @ runtime-core.esm-bundler.js:427
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queuePostFlushCb @ runtime-core.esm-bundler.js:336
queueEffectWithSuspense @ runtime-core.esm-bundler.js:7385
baseWatchOptions.scheduler @ runtime-core.esm-bundler.js:6261
effect2.scheduler @ reactivity.esm-bundler.js:1847
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:614
trigger @ reactivity.esm-bundler.js:588
set value @ reactivity.esm-bundler.js:1465
finalizeNavigation @ vue-router.mjs:3503
(匿名) @ vue-router.mjs:3368
Promise.then
pushWithRedirect @ vue-router.mjs:3335
push @ vue-router.mjs:3260
(匿名) @ index.ts:196
Promise.then
(匿名) @ index.ts:157
(匿名) @ vue-router.mjs:2107
runWithContext @ vue-router.mjs:2074
(匿名) @ vue-router.mjs:2107
(匿名) @ vue-router.mjs:2079
runWithContext @ runtime-core.esm-bundler.js:4024
runWithContext @ vue-router.mjs:3387
(匿名) @ vue-router.mjs:3743
Promise.then
(匿名) @ vue-router.mjs:3743
runGuardQueue @ vue-router.mjs:3743
(匿名) @ vue-router.mjs:3413
Promise.then
navigate @ vue-router.mjs:3406
pushWithRedirect @ vue-router.mjs:3327
push @ vue-router.mjs:3260
install @ vue-router.mjs:3704
use @ runtime-core.esm-bundler.js:3887
(匿名) @ main.ts:59
Promise.then
(匿名) @ main.ts:57
error.ts:9  Uncaught (in promise) ElementPlusError: [ElBacktop] target does not exist: .app-main .el-scrollbar__wrap
    at throwError (error.ts:9:9)
    at use-backtop.ts:26:9
    at runtime-core.esm-bundler.js:2836:40
    at callWithErrorHandling (runtime-core.esm-bundler.js:199:19)
    at callWithAsyncErrorHandling (runtime-core.esm-bundler.js:206:17)
    at hook.__weh.hook.__weh (runtime-core.esm-bundler.js:2816:19)
    at flushPostFlushCbs (runtime-core.esm-bundler.js:385:28)
    at flushJobs (runtime-core.esm-bundler.js:427:5)
throwError @ error.ts:9
(匿名) @ use-backtop.ts:26
(匿名) @ runtime-core.esm-bundler.js:2836
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
hook.__weh.hook.__weh @ runtime-core.esm-bundler.js:2816
flushPostFlushCbs @ runtime-core.esm-bundler.js:385
flushJobs @ runtime-core.esm-bundler.js:427
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queuePostFlushCb @ runtime-core.esm-bundler.js:336
queueEffectWithSuspense @ runtime-core.esm-bundler.js:7385
baseWatchOptions.scheduler @ runtime-core.esm-bundler.js:6261
effect2.scheduler @ reactivity.esm-bundler.js:1847
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:614
trigger @ reactivity.esm-bundler.js:588
set value @ reactivity.esm-bundler.js:1465
finalizeNavigation @ vue-router.mjs:3503
(匿名) @ vue-router.mjs:3368
Promise.then
pushWithRedirect @ vue-router.mjs:3335
push @ vue-router.mjs:3260
(匿名) @ index.ts:196
Promise.then
(匿名) @ index.ts:157
(匿名) @ vue-router.mjs:2107
runWithContext @ vue-router.mjs:2074
(匿名) @ vue-router.mjs:2107
(匿名) @ vue-router.mjs:2079
runWithContext @ runtime-core.esm-bundler.js:4024
runWithContext @ vue-router.mjs:3387
(匿名) @ vue-router.mjs:3743
Promise.then
(匿名) @ vue-router.mjs:3743
runGuardQueue @ vue-router.mjs:3743
(匿名) @ vue-router.mjs:3413
Promise.then
navigate @ vue-router.mjs:3406
pushWithRedirect @ vue-router.mjs:3327
push @ vue-router.mjs:3260
install @ vue-router.mjs:3704
use @ runtime-core.esm-bundler.js:3887
(匿名) @ main.ts:59
Promise.then
(匿名) @ main.ts:57
error.ts:9  Uncaught (in promise) ElementPlusError: [ElBacktop] target does not exist: .app-main .el-scrollbar__wrap
    at throwError (error.ts:9:9)
    at use-backtop.ts:26:9
    at runtime-core.esm-bundler.js:2836:40
    at callWithErrorHandling (runtime-core.esm-bundler.js:199:19)
    at callWithAsyncErrorHandling (runtime-core.esm-bundler.js:206:17)
    at hook.__weh.hook.__weh (runtime-core.esm-bundler.js:2816:19)
    at flushPostFlushCbs (runtime-core.esm-bundler.js:385:28)
    at flushJobs (runtime-core.esm-bundler.js:427:5)
throwError @ error.ts:9
(匿名) @ use-backtop.ts:26
(匿名) @ runtime-core.esm-bundler.js:2836
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
hook.__weh.hook.__weh @ runtime-core.esm-bundler.js:2816
flushPostFlushCbs @ runtime-core.esm-bundler.js:385
flushJobs @ runtime-core.esm-bundler.js:427
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queuePostFlushCb @ runtime-core.esm-bundler.js:336
queueEffectWithSuspense @ runtime-core.esm-bundler.js:7385
baseWatchOptions.scheduler @ runtime-core.esm-bundler.js:6261
effect2.scheduler @ reactivity.esm-bundler.js:1847
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:614
trigger @ reactivity.esm-bundler.js:588
set value @ reactivity.esm-bundler.js:1465
finalizeNavigation @ vue-router.mjs:3503
(匿名) @ vue-router.mjs:3368
Promise.then
pushWithRedirect @ vue-router.mjs:3335
push @ vue-router.mjs:3260
(匿名) @ index.ts:196
Promise.then
(匿名) @ index.ts:157
(匿名) @ vue-router.mjs:2107
runWithContext @ vue-router.mjs:2074
(匿名) @ vue-router.mjs:2107
(匿名) @ vue-router.mjs:2079
runWithContext @ runtime-core.esm-bundler.js:4024
runWithContext @ vue-router.mjs:3387
(匿名) @ vue-router.mjs:3743
Promise.then
(匿名) @ vue-router.mjs:3743
runGuardQueue @ vue-router.mjs:3743
(匿名) @ vue-router.mjs:3413
Promise.then
navigate @ vue-router.mjs:3406
pushWithRedirect @ vue-router.mjs:3327
push @ vue-router.mjs:3260
install @ vue-router.mjs:3704
use @ runtime-core.esm-bundler.js:3887
(匿名) @ main.ts:59
Promise.then
(匿名) @ main.ts:57
error.ts:9  Uncaught (in promise) ElementPlusError: [ElBacktop] target does not exist: .app-main .el-scrollbar__wrap
    at throwError (error.ts:9:9)
    at use-backtop.ts:26:9
    at runtime-core.esm-bundler.js:2836:40
    at callWithErrorHandling (runtime-core.esm-bundler.js:199:19)
    at callWithAsyncErrorHandling (runtime-core.esm-bundler.js:206:17)
    at hook.__weh.hook.__weh (runtime-core.esm-bundler.js:2816:19)
    at flushPostFlushCbs (runtime-core.esm-bundler.js:385:28)
    at flushJobs (runtime-core.esm-bundler.js:427:5)
throwError @ error.ts:9
(匿名) @ use-backtop.ts:26
(匿名) @ runtime-core.esm-bundler.js:2836
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
hook.__weh.hook.__weh @ runtime-core.esm-bundler.js:2816
flushPostFlushCbs @ runtime-core.esm-bundler.js:385
flushJobs @ runtime-core.esm-bundler.js:427
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queuePostFlushCb @ runtime-core.esm-bundler.js:336
queueEffectWithSuspense @ runtime-core.esm-bundler.js:7385
baseWatchOptions.scheduler @ runtime-core.esm-bundler.js:6261
effect2.scheduler @ reactivity.esm-bundler.js:1847
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:614
trigger @ reactivity.esm-bundler.js:588
set value @ reactivity.esm-bundler.js:1465
finalizeNavigation @ vue-router.mjs:3503
(匿名) @ vue-router.mjs:3368
Promise.then
pushWithRedirect @ vue-router.mjs:3335
push @ vue-router.mjs:3260
(匿名) @ index.ts:196
Promise.then
(匿名) @ index.ts:157
(匿名) @ vue-router.mjs:2107
runWithContext @ vue-router.mjs:2074
(匿名) @ vue-router.mjs:2107
(匿名) @ vue-router.mjs:2079
runWithContext @ runtime-core.esm-bundler.js:4024
runWithContext @ vue-router.mjs:3387
(匿名) @ vue-router.mjs:3743
Promise.then
(匿名) @ vue-router.mjs:3743
runGuardQueue @ vue-router.mjs:3743
(匿名) @ vue-router.mjs:3413
Promise.then
navigate @ vue-router.mjs:3406
pushWithRedirect @ vue-router.mjs:3327
push @ vue-router.mjs:3260
install @ vue-router.mjs:3704
use @ runtime-core.esm-bundler.js:3887
(匿名) @ main.ts:59
Promise.then
(匿名) @ main.ts:57
error.ts:9  Uncaught (in promise) ElementPlusError: [ElBacktop] target does not exist: .app-main .el-scrollbar__wrap
    at throwError (error.ts:9:9)
    at use-backtop.ts:26:9
    at runtime-core.esm-bundler.js:2836:40
    at callWithErrorHandling (runtime-core.esm-bundler.js:199:19)
    at callWithAsyncErrorHandling (runtime-core.esm-bundler.js:206:17)
    at hook.__weh.hook.__weh (runtime-core.esm-bundler.js:2816:19)
    at flushPostFlushCbs (runtime-core.esm-bundler.js:385:28)
    at flushJobs (runtime-core.esm-bundler.js:427:5)
throwError @ error.ts:9
(匿名) @ use-backtop.ts:26
(匿名) @ runtime-core.esm-bundler.js:2836
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
hook.__weh.hook.__weh @ runtime-core.esm-bundler.js:2816
flushPostFlushCbs @ runtime-core.esm-bundler.js:385
flushJobs @ runtime-core.esm-bundler.js:427
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queuePostFlushCb @ runtime-core.esm-bundler.js:336
queueEffectWithSuspense @ runtime-core.esm-bundler.js:7385
baseWatchOptions.scheduler @ runtime-core.esm-bundler.js:6261
effect2.scheduler @ reactivity.esm-bundler.js:1847
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:614
trigger @ reactivity.esm-bundler.js:588
set value @ reactivity.esm-bundler.js:1465
finalizeNavigation @ vue-router.mjs:3503
(匿名) @ vue-router.mjs:3368
Promise.then
pushWithRedirect @ vue-router.mjs:3335
push @ vue-router.mjs:3260
(匿名) @ index.ts:196
Promise.then
(匿名) @ index.ts:157
(匿名) @ vue-router.mjs:2107
runWithContext @ vue-router.mjs:2074
(匿名) @ vue-router.mjs:2107
(匿名) @ vue-router.mjs:2079
runWithContext @ runtime-core.esm-bundler.js:4024
runWithContext @ vue-router.mjs:3387
(匿名) @ vue-router.mjs:3743
Promise.then
(匿名) @ vue-router.mjs:3743
runGuardQueue @ vue-router.mjs:3743
(匿名) @ vue-router.mjs:3413
Promise.then
navigate @ vue-router.mjs:3406
pushWithRedirect @ vue-router.mjs:3327
push @ vue-router.mjs:3260
install @ vue-router.mjs:3704
use @ runtime-core.esm-bundler.js:3887
(匿名) @ main.ts:59
Promise.then
(匿名) @ main.ts:57
