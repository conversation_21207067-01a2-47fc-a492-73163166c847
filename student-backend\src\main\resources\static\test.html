<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, button {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            cursor: pointer;
            margin-top: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .response {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 15px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            border-color: #28a745;
            background-color: #d4edda;
        }
        .error {
            border-color: #dc3545;
            background-color: #f8d7da;
        }
    </style>
</head>
<body>
    <h1>学生管理系统 API 测试</h1>
    
    <!-- 登录测试 -->
    <div class="container">
        <h2>1. 登录测试</h2>
        <div class="form-group">
            <label for="username">用户名:</label>
            <input type="text" id="username" value="admin" placeholder="请输入用户名">
        </div>
        <div class="form-group">
            <label for="password">密码:</label>
            <input type="password" id="password" value="admin123" placeholder="请输入密码">
        </div>
        <button onclick="testLogin()">登录</button>
        <div id="loginResponse" class="response" style="display: none;"></div>
    </div>

    <!-- 菜单测试 -->
    <div class="container">
        <h2>2. 菜单列表测试</h2>
        <button onclick="testMenuList()">获取菜单列表</button>
        <div id="menuResponse" class="response" style="display: none;"></div>
    </div>

    <!-- 动态路由测试 -->
    <div class="container">
        <h2>3. 动态路由测试</h2>
        <button onclick="testAsyncRoutes()">获取动态路由</button>
        <div id="routesResponse" class="response" style="display: none;"></div>
    </div>

    <!-- Token刷新测试 -->
    <div class="container">
        <h2>4. Token刷新测试</h2>
        <button onclick="testRefreshToken()">刷新Token</button>
        <div id="refreshResponse" class="response" style="display: none;"></div>
    </div>

    <script>
        let currentToken = '';
        let currentRefreshToken = '';

        // 登录测试
        async function testLogin() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            try {
                const response = await fetch('/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ username, password })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    currentToken = data.data.accessToken;
                    currentRefreshToken = data.data.refreshToken;
                    showResponse('loginResponse', data, true);
                } else {
                    showResponse('loginResponse', data, false);
                }
            } catch (error) {
                showResponse('loginResponse', { error: error.message }, false);
            }
        }

        // 菜单列表测试
        async function testMenuList() {
            try {
                const response = await fetch('/menu', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${currentToken}`
                    },
                    body: JSON.stringify({})
                });
                
                const data = await response.json();
                showResponse('menuResponse', data, data.success);
            } catch (error) {
                showResponse('menuResponse', { error: error.message }, false);
            }
        }

        // 动态路由测试
        async function testAsyncRoutes() {
            try {
                const response = await fetch('/get-async-routes', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${currentToken}`
                    }
                });
                
                const data = await response.json();
                showResponse('routesResponse', data, data.success);
            } catch (error) {
                showResponse('routesResponse', { error: error.message }, false);
            }
        }

        // Token刷新测试
        async function testRefreshToken() {
            try {
                const response = await fetch('/refresh-token', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ refreshToken: currentRefreshToken })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    currentToken = data.data.accessToken;
                    currentRefreshToken = data.data.refreshToken;
                    showResponse('refreshResponse', data, true);
                } else {
                    showResponse('refreshResponse', data, false);
                }
            } catch (error) {
                showResponse('refreshResponse', { error: error.message }, false);
            }
        }

        // 显示响应结果
        function showResponse(elementId, data, isSuccess) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `response ${isSuccess ? 'success' : 'error'}`;
            element.textContent = JSON.stringify(data, null, 2);
        }
    </script>
</body>
</html>
