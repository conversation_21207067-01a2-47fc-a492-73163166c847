package com.example.mapper.basic;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.example.entity.basic.Classes;
import com.example.dto.basic.ClassesQueryDTO;
import com.example.vo.basic.ClassesVO;
import com.example.vo.educational.CourseVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 班级Mapper
 *
 * <AUTHOR>
 * @since 2025-07-27
 */
@Mapper
public interface ClassesMapper extends BaseMapper<Classes> {

    /**
     * 分页查询班级列表（带关联信息）
     *
     * @param page 分页参数
     * @param query 查询条件
     * @return 班级列表
     */
    IPage<ClassesVO> selectClassesPage(IPage<ClassesVO> page, @Param("query") ClassesQueryDTO query);

    /**
     * 根据ID查询班级详情（带关联信息）
     *
     * @param id 班级ID
     * @return 班级详情
     */
    ClassesVO selectClassesById(@Param("id") Integer id);

    /**
     * 查询所有班级列表（用于下拉选择）
     *
     * @return 班级列表
     */
    List<ClassesVO> selectAllClasses();

    /**
     * 根据专业代码查询班级列表
     *
     * @param majorCode 专业代码
     * @return 班级列表
     */
    List<ClassesVO> selectClassesByMajorCode(@Param("majorCode") String majorCode);

    /**
     * 根据班主任工号查询班级列表
     *
     * @param headTeacherCode 班主任工号
     * @return 班级列表
     */
    List<ClassesVO> selectClassesByHeadTeacher(@Param("headTeacherCode") String headTeacherCode);

    /**
     * 检查班级代码是否存在
     *
     * @param classCode 班级代码
     * @param excludeId 排除的班级ID
     * @return 是否存在
     */
    boolean existsClassCode(@Param("classCode") String classCode, @Param("excludeId") Integer excludeId);

    /**
     * 更新班级学生人数
     *
     * @param classId 班级ID
     * @param studentCount 学生人数
     */
    void updateStudentCount(@Param("classId") Integer classId, @Param("studentCount") Integer studentCount);

    /**
     * 获取班级在指定学期的课程列表
     *
     * @param classCode 班级代码
     * @param semesterId 学期ID
     * @return 课程列表
     */
    List<CourseVO> selectCoursesByClassAndSemester(@Param("classCode") String classCode, @Param("semesterId") Integer semesterId);
}
