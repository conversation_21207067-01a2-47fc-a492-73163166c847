package com.example.common.export.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 文件响应工具类
 * 统一处理文件下载响应和文件名生成
 *
 * <AUTHOR>
 * @since 2025-01-30
 */
@Slf4j
public class FileResponseUtils {

    private static final String DEFAULT_EXCEL_CONTENT_TYPE = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
    private static final String DEFAULT_CHARSET = "UTF-8";
    private static final DateTimeFormatter TIMESTAMP_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss");

    /**
     * 创建Excel文件下载响应
     *
     * @param data     文件数据
     * @param fileName 文件名（不包含扩展名）
     * @return ResponseEntity
     */
    public static ResponseEntity<Resource> createExcelDownloadResponse(byte[] data, String fileName) {
        return createExcelDownloadResponse(data, fileName, true);
    }

    /**
     * 创建Excel文件下载响应
     *
     * @param data              文件数据
     * @param fileName          文件名（不包含扩展名）
     * @param includeTimestamp  是否包含时间戳
     * @return ResponseEntity
     */
    public static ResponseEntity<Resource> createExcelDownloadResponse(byte[] data, String fileName, boolean includeTimestamp) {
        try {
            String fullFileName = generateFileName(fileName, "xlsx", includeTimestamp);
            String encodedFileName = encodeFileName(fullFileName);
            
            ByteArrayResource resource = new ByteArrayResource(data);
            
            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename*=UTF-8''" + encodedFileName)
                    .header(HttpHeaders.CONTENT_TYPE, DEFAULT_EXCEL_CONTENT_TYPE)
                    .header(HttpHeaders.CONTENT_LENGTH, String.valueOf(data.length))
                    .header(HttpHeaders.CACHE_CONTROL, "no-cache, no-store, must-revalidate")
                    .header(HttpHeaders.PRAGMA, "no-cache")
                    .header(HttpHeaders.EXPIRES, "0")
                    .contentType(MediaType.parseMediaType(DEFAULT_EXCEL_CONTENT_TYPE))
                    .body(resource);
                    
        } catch (Exception e) {
            log.error("创建Excel下载响应失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 创建CSV文件下载响应
     *
     * @param data     文件数据
     * @param fileName 文件名（不包含扩展名）
     * @return ResponseEntity
     */
    public static ResponseEntity<Resource> createCsvDownloadResponse(byte[] data, String fileName) {
        return createCsvDownloadResponse(data, fileName, true);
    }

    /**
     * 创建CSV文件下载响应
     *
     * @param data              文件数据
     * @param fileName          文件名（不包含扩展名）
     * @param includeTimestamp  是否包含时间戳
     * @return ResponseEntity
     */
    public static ResponseEntity<Resource> createCsvDownloadResponse(byte[] data, String fileName, boolean includeTimestamp) {
        try {
            String fullFileName = generateFileName(fileName, "csv", includeTimestamp);
            String encodedFileName = encodeFileName(fullFileName);
            
            ByteArrayResource resource = new ByteArrayResource(data);
            
            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename*=UTF-8''" + encodedFileName)
                    .header(HttpHeaders.CONTENT_TYPE, "text/csv; charset=UTF-8")
                    .header(HttpHeaders.CONTENT_LENGTH, String.valueOf(data.length))
                    .header(HttpHeaders.CACHE_CONTROL, "no-cache, no-store, must-revalidate")
                    .header(HttpHeaders.PRAGMA, "no-cache")
                    .header(HttpHeaders.EXPIRES, "0")
                    .contentType(MediaType.parseMediaType("text/csv"))
                    .body(resource);
                    
        } catch (Exception e) {
            log.error("创建CSV下载响应失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 生成文件名
     *
     * @param baseName         基础文件名
     * @param extension        文件扩展名
     * @param includeTimestamp 是否包含时间戳
     * @return 完整文件名
     */
    public static String generateFileName(String baseName, String extension, boolean includeTimestamp) {
        StringBuilder fileName = new StringBuilder(baseName);
        
        if (includeTimestamp) {
            fileName.append("_").append(LocalDateTime.now().format(TIMESTAMP_FORMATTER));
        }
        
        fileName.append(".").append(extension);
        
        return fileName.toString();
    }

    /**
     * 生成带参数的文件名
     *
     * @param baseName         基础文件名
     * @param extension        文件扩展名
     * @param includeTimestamp 是否包含时间戳
     * @param params           额外参数
     * @return 完整文件名
     */
    public static String generateFileName(String baseName, String extension, boolean includeTimestamp, String... params) {
        StringBuilder fileName = new StringBuilder(baseName);
        
        // 添加参数
        for (String param : params) {
            if (param != null && !param.trim().isEmpty()) {
                fileName.append("_").append(param.trim());
            }
        }
        
        if (includeTimestamp) {
            fileName.append("_").append(LocalDateTime.now().format(TIMESTAMP_FORMATTER));
        }
        
        fileName.append(".").append(extension);
        
        return fileName.toString();
    }

    /**
     * 编码文件名，处理中文字符
     *
     * @param fileName 原始文件名
     * @return 编码后的文件名
     */
    public static String encodeFileName(String fileName) {
        try {
            return URLEncoder.encode(fileName, DEFAULT_CHARSET).replaceAll("\\+", "%20");
        } catch (UnsupportedEncodingException e) {
            log.warn("文件名编码失败，使用默认文件名: {}", e.getMessage());
            return "download_" + System.currentTimeMillis() + ".xlsx";
        }
    }

    /**
     * 验证文件名是否安全
     *
     * @param fileName 文件名
     * @return 是否安全
     */
    public static boolean isFileNameSafe(String fileName) {
        if (fileName == null || fileName.trim().isEmpty()) {
            return false;
        }
        
        // 检查是否包含危险字符
        String[] dangerousChars = {"..", "/", "\\", ":", "*", "?", "\"", "<", ">", "|"};
        for (String dangerousChar : dangerousChars) {
            if (fileName.contains(dangerousChar)) {
                return false;
            }
        }
        
        return true;
    }

    /**
     * 清理文件名，移除不安全字符
     *
     * @param fileName 原始文件名
     * @return 清理后的文件名
     */
    public static String sanitizeFileName(String fileName) {
        if (fileName == null) {
            return "download";
        }
        
        // 移除或替换不安全字符
        return fileName.replaceAll("[.]{2,}", "")
                      .replaceAll("[/\\\\:*?\"<>|]", "_")
                      .trim();
    }

    /**
     * 获取文件大小的可读格式
     *
     * @param bytes 字节数
     * @return 可读格式的文件大小
     */
    public static String getReadableFileSize(long bytes) {
        if (bytes < 1024) {
            return bytes + " B";
        } else if (bytes < 1024 * 1024) {
            return String.format("%.1f KB", bytes / 1024.0);
        } else if (bytes < 1024 * 1024 * 1024) {
            return String.format("%.1f MB", bytes / (1024.0 * 1024.0));
        } else {
            return String.format("%.1f GB", bytes / (1024.0 * 1024.0 * 1024.0));
        }
    }
}
