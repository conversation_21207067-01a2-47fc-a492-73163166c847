package com.example.service.student;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.example.dto.student.StudentsQueryDTO;
import com.example.entity.student.Students;
import com.example.vo.student.StudentsVO;

import java.util.List;

/**
 * 学生信息Service接口
 */
public interface StudentsService extends IService<Students> {
    
    /**
     * 分页查询学生列表
     *
     * @param queryDTO 查询条件
     * @return 学生列表
     */
    IPage<StudentsVO> getStudentsPage(StudentsQueryDTO queryDTO);
    
    /**
     * 根据ID查询学生详情
     *
     * @param id 学生ID
     * @return 学生详情
     */
    StudentsVO getStudentsById(Integer id);
    
    /**
     * 查询所有学生列表
     *
     * @return 学生列表
     */
    List<StudentsVO> getAllStudents();
    
    /**
     * 根据班级代码查询学生列表
     *
     * @param classCode 班级代码
     * @return 学生列表
     */
    List<StudentsVO> getStudentsByClassCode(String classCode);
    
    /**
     * 根据专业代码查询学生列表
     *
     * @param majorCode 专业代码
     * @return 学生列表
     */
    List<StudentsVO> getStudentsByMajorCode(String majorCode);
    
    /**
     * 新增学生
     *
     * @param studentsVO 学生信息
     */
    void saveStudents(StudentsVO studentsVO);
    
    /**
     * 更新学生
     *
     * @param studentsVO 学生信息
     */
    void updateStudents(StudentsVO studentsVO);
    
    /**
     * 删除学生
     *
     * @param id 学生ID
     */
    void deleteStudents(Integer id);
    
    /**
     * 批量删除学生
     *
     * @param ids 学生ID列表
     */
    void batchDeleteStudents(List<Integer> ids);
}
