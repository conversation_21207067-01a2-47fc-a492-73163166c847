package com.example.common.export;

import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * Apache POI 流式Excel导出服务
 * 专门用于处理大数据量（百万行）的Excel导出
 * 使用SXSSF（Streaming Usermodel API）来优化内存使用
 * 支持表头高度自适应和学业成绩、绩点保留2位小数
 *
 * <AUTHOR>
 * @since 2025-01-30
 */
@Service
@Slf4j
public class ApachePoiExportService {

    /**
     * 内存中保留的行数（窗口大小）
     * 超过这个数量的行会被写入临时文件，释放内存
     */
    private static final int WINDOW_SIZE = 1000;

    /**
     * 导出成绩数据到Excel（支持百万行数据）
     *
     * @param data      导出数据
     * @param headers   表头映射 (字段名 -> 显示名称)
     * @param sheetName 工作表名称
     * @return Excel文件字节数组
     */
    public byte[] exportGradesToExcel(List<Map<String, Object>> data, Map<String, String> headers, String sheetName) {
        log.info("开始Apache POI流式导出，数据行数: {}, 表头数量: {}", data.size(), headers.size());

        // 使用SXSSF工作簿，支持大数据量导出
        try (SXSSFWorkbook workbook = new SXSSFWorkbook(WINDOW_SIZE);
             ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {

            // 创建工作表
            Sheet sheet = workbook.createSheet(sheetName);

            // 创建样式
            CellStyle headerStyle = createHeaderStyle(workbook);
            CellStyle dataStyle = createDataStyle(workbook);
            CellStyle numberStyle = createNumberStyle(workbook);

            // 构建字段顺序
            List<String> fieldOrder = new ArrayList<>(headers.keySet());

            // 创建表头行
            createHeaderRow(sheet, headers, fieldOrder, headerStyle);

            // 填充数据行
            fillDataRows(sheet, data, fieldOrder, dataStyle, numberStyle);

            // 自动调整所有列宽
            autoSizeColumns(sheet, fieldOrder.size());

            // 写入输出流
            workbook.write(outputStream);

            byte[] result = outputStream.toByteArray();
            log.info("Apache POI导出成功，数据行数: {}, 文件大小: {} bytes", data.size(), result.length);

            return result;

        } catch (IOException e) {
            log.error("Apache POI导出失败", e);
            throw new RuntimeException("Excel导出失败: " + e.getMessage(), e);
        }
    }

    /**
     * 创建表头样式
     */
    private CellStyle createHeaderStyle(Workbook workbook) {
        CellStyle headerStyle = workbook.createCellStyle();

        // 设置背景色
        headerStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        // 设置边框
        headerStyle.setBorderTop(BorderStyle.THIN);
        headerStyle.setBorderBottom(BorderStyle.THIN);
        headerStyle.setBorderLeft(BorderStyle.THIN);
        headerStyle.setBorderRight(BorderStyle.THIN);

        // 设置对齐方式
        headerStyle.setAlignment(HorizontalAlignment.CENTER);
        headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        // 设置字体 - 宋体，10号字体
        Font font = workbook.createFont();
        font.setFontName("宋体");
        font.setBold(true);
        font.setFontHeightInPoints((short) 10); // 10号字体
        font.setColor(IndexedColors.BLACK.getIndex());
        headerStyle.setFont(font);

        // 设置自动换行
        headerStyle.setWrapText(true);

        return headerStyle;
    }

    /**
     * 创建数据样式
     */
    private CellStyle createDataStyle(Workbook workbook) {
        CellStyle dataStyle = workbook.createCellStyle();

        // 设置边框
        dataStyle.setBorderTop(BorderStyle.THIN);
        dataStyle.setBorderBottom(BorderStyle.THIN);
        dataStyle.setBorderLeft(BorderStyle.THIN);
        dataStyle.setBorderRight(BorderStyle.THIN);

        // 设置对齐方式
        dataStyle.setAlignment(HorizontalAlignment.CENTER);
        dataStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        // 设置字体 - 宋体，9号字体
        Font font = workbook.createFont();
        font.setFontName("宋体");
        font.setFontHeightInPoints((short) 9); // 9号字体
        font.setColor(IndexedColors.BLACK.getIndex());
        dataStyle.setFont(font);

        return dataStyle;
    }

    /**
     * 创建数字样式（保留2位小数）
     */
    private CellStyle createNumberStyle(Workbook workbook) {
        CellStyle numberStyle = workbook.createCellStyle();

        // 设置边框
        numberStyle.setBorderTop(BorderStyle.THIN);
        numberStyle.setBorderBottom(BorderStyle.THIN);
        numberStyle.setBorderLeft(BorderStyle.THIN);
        numberStyle.setBorderRight(BorderStyle.THIN);

        // 设置对齐方式
        numberStyle.setAlignment(HorizontalAlignment.CENTER);
        numberStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        // 设置字体 - 宋体，10号字体
        Font font = workbook.createFont();
        font.setFontName("宋体");
        font.setFontHeightInPoints((short) 10);
        font.setColor(IndexedColors.BLACK.getIndex());
        numberStyle.setFont(font);

        // 设置数字格式为保留2位小数
        DataFormat format = workbook.createDataFormat();
        numberStyle.setDataFormat(format.getFormat("0.00"));

        return numberStyle;
    }

    /**
     * 创建表头行
     */
    private void createHeaderRow(Sheet sheet, Map<String, String> headers, List<String> fieldOrder, CellStyle headerStyle) {
        Row headerRow = sheet.createRow(0);

        // 计算表头内容的最大行数，用于自适应行高
        int maxLines = 1;
        for (String fieldName : fieldOrder) {
            String displayName = headers.get(fieldName);
            if (displayName != null) {
                // 计算换行符数量
                int lines = displayName.split("\n").length;
                maxLines = Math.max(maxLines, lines);
            }
        }

        // 根据内容行数和字体大小自适应设置行高
        // 11号字体约为330个单位，加上边距和行间距
        // 对于三行表头，需要更大的行高来确保完全显示
        short rowHeight = (short) (maxLines * 500 + 300);
        headerRow.setHeight(rowHeight);

        for (int i = 0; i < fieldOrder.size(); i++) {
            String fieldName = fieldOrder.get(i);
            String displayName = headers.get(fieldName);

            Cell cell = headerRow.createCell(i);
            cell.setCellValue(displayName);
            cell.setCellStyle(headerStyle);
        }
    }

    /**
     * 填充数据行
     */
    private void fillDataRows(Sheet sheet, List<Map<String, Object>> data, List<String> fieldOrder, CellStyle dataStyle, CellStyle numberStyle) {
        for (int rowIndex = 0; rowIndex < data.size(); rowIndex++) {
            Map<String, Object> rowData = data.get(rowIndex);
            Row row = sheet.createRow(rowIndex + 1); // +1 因为第0行是表头

            for (int colIndex = 0; colIndex < fieldOrder.size(); colIndex++) {
                String fieldName = fieldOrder.get(colIndex);
                Object value = rowData.get(fieldName);

                Cell cell = row.createCell(colIndex);
                setCellValue(cell, value);

                // 根据字段名判断是否使用数字样式
                if (isScoreOrGpaField(fieldName) && value instanceof Number) {
                    cell.setCellStyle(numberStyle);
                } else {
                    cell.setCellStyle(dataStyle);
                }
            }
        }
    }

    /**
     * 判断是否为学业成绩或绩点字段
     */
    private boolean isScoreOrGpaField(String fieldName) {
        if (fieldName == null) return false;
        String lowerFieldName = fieldName.toLowerCase();
        return lowerFieldName.contains("score") ||
               lowerFieldName.contains("grade") ||
               lowerFieldName.contains("gpa") ||
               lowerFieldName.contains("绩点") ||
               lowerFieldName.contains("成绩") ||
               lowerFieldName.contains("分数");
    }

    /**
     * 设置单元格值
     */
    private void setCellValue(Cell cell, Object value) {
        if (value == null) {
            cell.setCellValue("");
        } else if (value instanceof Number) {
            cell.setCellValue(((Number) value).doubleValue());
        } else if (value instanceof Boolean) {
            cell.setCellValue((Boolean) value);
        } else {
            cell.setCellValue(value.toString());
        }
    }

    /**
     * 自动调整所有列宽
     */
    private void autoSizeColumns(Sheet sheet, int maxColumns) {
        for (int i = 0; i < maxColumns; i++) {
            try {
                sheet.autoSizeColumn(i);
                // 设置最大列宽，避免过宽
                int columnWidth = sheet.getColumnWidth(i);
                if (columnWidth > 6000) { // 约30个字符宽度
                    sheet.setColumnWidth(i, 6000);
                }
            } catch (Exception e) {
                log.warn("自动调整第{}列宽度失败: {}", i, e.getMessage());
                // 设置默认宽度
                sheet.setColumnWidth(i, 3000);
            }
        }
    }

    /**
     * 生成Excel模板
     *
     * @param headers   表头映射 (字段名 -> 显示名称)
     * @param sheetName 工作表名称
     * @return Excel模板文件字节数组
     */
    public byte[] generateTemplate(Map<String, String> headers, String sheetName) {
        log.info("开始生成Excel模板，表头数量: {}", headers.size());

        try (SXSSFWorkbook workbook = new SXSSFWorkbook(WINDOW_SIZE);
             ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {

            // 创建工作表
            Sheet sheet = workbook.createSheet(sheetName);

            // 创建样式
            CellStyle headerStyle = createHeaderStyle(workbook);

            // 构建字段顺序
            List<String> fieldOrder = new ArrayList<>(headers.keySet());

            // 创建表头行
            createHeaderRow(sheet, headers, fieldOrder, headerStyle);

            // 自动调整所有列宽
            autoSizeColumns(sheet, fieldOrder.size());

            // 写入输出流
            workbook.write(outputStream);

            byte[] result = outputStream.toByteArray();
            log.info("Excel模板生成成功，文件大小: {} bytes", result.length);

            return result;

        } catch (Exception e) {
            log.error("生成Excel模板失败", e);
            throw new RuntimeException("生成Excel模板失败: " + e.getMessage());
        }
    }

    /**
     * 导入Excel数据
     *
     * @param file      上传的Excel文件
     * @param headers   表头映射 (字段名 -> 显示名称)
     * @param sheetName 工作表名称
     * @return 导入的数据列表
     */
    public List<Map<String, Object>> importExcelData(MultipartFile file, Map<String, String> headers, String sheetName) {
        log.info("开始导入Excel数据，文件: {}, 大小: {} bytes", file.getOriginalFilename(), file.getSize());

        // 验证文件
        validateExcelFile(file);

        try (InputStream inputStream = file.getInputStream();
             Workbook workbook = new XSSFWorkbook(inputStream)) {

            Sheet sheet = workbook.getSheet(sheetName);
            if (sheet == null) {
                sheet = workbook.getSheetAt(0); // 如果指定工作表不存在，使用第一个工作表
            }

            // 验证表头
            validateHeaders(sheet, headers);

            // 读取数据
            List<Map<String, Object>> dataList = readDataFromSheet(sheet, headers);

            log.info("Excel数据导入成功，共读取 {} 行数据", dataList.size());
            return dataList;

        } catch (Exception e) {
            log.error("导入Excel数据失败", e);
            throw new RuntimeException("导入Excel数据失败: " + e.getMessage());
        }
    }

    /**
     * 验证Excel文件
     */
    private void validateExcelFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new IllegalArgumentException("文件不能为空");
        }

        String fileName = file.getOriginalFilename();
        if (fileName == null || (!fileName.endsWith(".xlsx") && !fileName.endsWith(".xls"))) {
            throw new IllegalArgumentException("请上传Excel文件（.xlsx或.xls格式）");
        }

        // 文件大小限制（10MB）
        if (file.getSize() > 10 * 1024 * 1024) {
            throw new IllegalArgumentException("文件大小不能超过10MB");
        }
    }

    /**
     * 验证表头
     */
    private void validateHeaders(Sheet sheet, Map<String, String> headers) {
        Row headerRow = sheet.getRow(0);
        if (headerRow == null) {
            throw new IllegalArgumentException("Excel文件表头不能为空");
        }

        List<String> expectedHeaders = new ArrayList<>(headers.values());
        List<String> actualHeaders = new ArrayList<>();

        for (Cell cell : headerRow) {
            if (cell != null) {
                actualHeaders.add(getCellStringValue(cell));
            }
        }

        // 检查必需的表头是否存在
        for (String expectedHeader : expectedHeaders) {
            if (!actualHeaders.contains(expectedHeader)) {
                throw new IllegalArgumentException("缺少必需的表头: " + expectedHeader);
            }
        }
    }

    /**
     * 从工作表读取数据
     */
    private List<Map<String, Object>> readDataFromSheet(Sheet sheet, Map<String, String> headers) {
        List<Map<String, Object>> dataList = new ArrayList<>();

        // 获取表头行
        Row headerRow = sheet.getRow(0);
        if (headerRow == null) {
            return dataList;
        }

        // 建立表头索引映射
        Map<String, Integer> headerIndexMap = buildHeaderIndexMap(headerRow, headers);

        // 读取数据行
        for (int rowIndex = 1; rowIndex <= sheet.getLastRowNum(); rowIndex++) {
            Row row = sheet.getRow(rowIndex);
            if (row == null || isEmptyRow(row)) {
                continue;
            }

            Map<String, Object> rowData = new LinkedHashMap<>();
            boolean hasData = false;

            for (Map.Entry<String, String> entry : headers.entrySet()) {
                String fieldName = entry.getKey();
                String headerName = entry.getValue();
                Integer columnIndex = headerIndexMap.get(headerName);

                if (columnIndex != null) {
                    Cell cell = row.getCell(columnIndex);
                    Object value = getCellValue(cell);
                    rowData.put(fieldName, value);
                    if (value != null && !value.toString().trim().isEmpty()) {
                        hasData = true;
                    }
                }
            }

            if (hasData) {
                dataList.add(rowData);
            }
        }

        return dataList;
    }

    /**
     * 建立表头索引映射
     */
    private Map<String, Integer> buildHeaderIndexMap(Row headerRow, Map<String, String> headers) {
        Map<String, Integer> indexMap = new LinkedHashMap<>();

        for (Cell cell : headerRow) {
            if (cell != null) {
                String headerName = getCellStringValue(cell);
                if (headers.containsValue(headerName)) {
                    indexMap.put(headerName, cell.getColumnIndex());
                }
            }
        }

        return indexMap;
    }

    /**
     * 判断行是否为空
     */
    private boolean isEmptyRow(Row row) {
        for (Cell cell : row) {
            if (cell != null && cell.getCellType() != CellType.BLANK) {
                String value = getCellStringValue(cell);
                if (value != null && !value.trim().isEmpty()) {
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * 获取单元格值
     */
    private Object getCellValue(Cell cell) {
        if (cell == null) {
            return null;
        }

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue().trim();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue();
                } else {
                    double numericValue = cell.getNumericCellValue();
                    // 如果是整数，返回Long，否则返回BigDecimal
                    if (numericValue == Math.floor(numericValue)) {
                        return (long) numericValue;
                    } else {
                        return BigDecimal.valueOf(numericValue);
                    }
                }
            case BOOLEAN:
                return cell.getBooleanCellValue();
            case FORMULA:
                try {
                    return cell.getNumericCellValue();
                } catch (Exception e) {
                    return cell.getStringCellValue();
                }
            default:
                return null;
        }
    }

    /**
     * 获取单元格字符串值
     */
    private String getCellStringValue(Cell cell) {
        if (cell == null) {
            return "";
        }

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue().trim();
            case NUMERIC:
                return String.valueOf((long) cell.getNumericCellValue());
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                try {
                    return String.valueOf((long) cell.getNumericCellValue());
                } catch (Exception e) {
                    return cell.getStringCellValue().trim();
                }
            default:
                return "";
        }
    }
}
