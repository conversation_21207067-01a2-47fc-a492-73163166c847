2025-07-31 00:00:17.122 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 1 class path change (1 addition, 0 deletions, 0 modifications)
2025-07-31 00:00:17.122 [Thread-179] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-31 00:00:17.122 [tomcat-shutdown] INFO  org.apache.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8080"]
2025-07-31 00:00:17.155 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
2025-07-31 00:00:17.155 [Thread-179] INFO  org.apache.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8080"]
2025-07-31 00:00:17.155 [Thread-179] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-34} closing ...
2025-07-31 00:00:17.155 [Thread-179] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-34} closed
2025-07-31 00:00:17.322 [restartedMain] INFO  com.example.StudentManagementApplication - Starting StudentManagementApplication using Java 22.0.1 with PID 7196 (C:\Users\<USER>\Desktop\vite3\student-backend\target\classes started by 张 in C:\Users\<USER>\Desktop\vite3\student-backend)
2025-07-31 00:00:17.322 [restartedMain] INFO  com.example.StudentManagementApplication - The following 1 profile is active: "test"
2025-07-31 00:00:17.924 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-07-31 00:00:17.924 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-31 00:00:17.924 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-31 00:00:17.924 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-07-31 00:00:17.943 [restartedMain] INFO  o.a.c.c.ContainerBase.[Tomcat-4].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-31 00:00:17.943 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 621 ms
2025-07-31 00:00:18.397 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-35} inited
2025-07-31 00:00:21.660 [restartedMain] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-07-31 00:00:21.700 [restartedMain] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: e0d1111b-1e74-4a30-a552-c766fd1a816b

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-31 00:00:21.702 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-07-31 00:00:22.116 [restartedMain] INFO  o.s.b.d.autoconfigure.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-31 00:00:22.148 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-07-31 00:00:22.164 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/'
2025-07-31 00:00:22.164 [restartedMain] INFO  com.example.StudentManagementApplication - Started StudentManagementApplication in 4.854 seconds (process running for 2691.274)
2025-07-31 00:00:22.164 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-07-31 00:00:56.725 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 1 class path change (1 addition, 0 deletions, 0 modifications)
2025-07-31 00:00:56.727 [Thread-184] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-31 00:00:56.727 [tomcat-shutdown] INFO  org.apache.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8080"]
2025-07-31 00:00:56.732 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
2025-07-31 00:00:56.732 [Thread-184] INFO  org.apache.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8080"]
2025-07-31 00:00:56.737 [Thread-184] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-35} closing ...
2025-07-31 00:00:56.738 [Thread-184] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-35} closed
2025-07-31 00:00:56.872 [restartedMain] INFO  com.example.StudentManagementApplication - Starting StudentManagementApplication using Java 22.0.1 with PID 7196 (C:\Users\<USER>\Desktop\vite3\student-backend\target\classes started by 张 in C:\Users\<USER>\Desktop\vite3\student-backend)
2025-07-31 00:00:56.872 [restartedMain] INFO  com.example.StudentManagementApplication - The following 1 profile is active: "test"
2025-07-31 00:00:57.438 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-07-31 00:00:57.438 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-31 00:00:57.439 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-31 00:00:57.439 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-07-31 00:00:57.479 [restartedMain] INFO  o.a.c.c.ContainerBase.[Tomcat-4].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-31 00:00:57.479 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 605 ms
2025-07-31 00:00:57.956 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-36} inited
2025-07-31 00:00:59.687 [restartedMain] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-07-31 00:00:59.719 [restartedMain] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: e9ce1840-14ba-4193-84ab-3608086b44b5

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-31 00:00:59.719 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-07-31 00:01:00.111 [restartedMain] INFO  o.s.b.d.autoconfigure.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-31 00:01:00.144 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-07-31 00:01:00.149 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/'
2025-07-31 00:01:00.156 [restartedMain] INFO  com.example.StudentManagementApplication - Started StudentManagementApplication in 3.312 seconds (process running for 2729.258)
2025-07-31 00:01:00.158 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-07-31 00:01:39.346 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 1 class path change (1 addition, 0 deletions, 0 modifications)
2025-07-31 00:01:39.348 [Thread-189] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-31 00:01:39.348 [tomcat-shutdown] INFO  org.apache.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8080"]
2025-07-31 00:01:39.352 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
2025-07-31 00:01:39.352 [Thread-189] INFO  org.apache.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8080"]
2025-07-31 00:01:39.356 [Thread-189] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-36} closing ...
2025-07-31 00:01:39.356 [Thread-189] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-36} closed
2025-07-31 00:01:39.510 [restartedMain] INFO  com.example.StudentManagementApplication - Starting StudentManagementApplication using Java 22.0.1 with PID 7196 (C:\Users\<USER>\Desktop\vite3\student-backend\target\classes started by 张 in C:\Users\<USER>\Desktop\vite3\student-backend)
2025-07-31 00:01:39.510 [restartedMain] INFO  com.example.StudentManagementApplication - The following 1 profile is active: "test"
2025-07-31 00:01:40.095 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-07-31 00:01:40.095 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-31 00:01:40.095 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-31 00:01:40.095 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-07-31 00:01:40.144 [restartedMain] INFO  o.a.c.c.ContainerBase.[Tomcat-4].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-31 00:01:40.144 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 632 ms
2025-07-31 00:01:40.625 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-37} inited
2025-07-31 00:01:42.171 [restartedMain] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-07-31 00:01:42.219 [restartedMain] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: ab05d798-081b-4e03-8bdf-af429143e436

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-31 00:01:42.219 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-07-31 00:01:42.564 [restartedMain] INFO  o.s.b.d.autoconfigure.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-31 00:01:42.598 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-07-31 00:01:42.635 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/'
2025-07-31 00:01:42.643 [restartedMain] INFO  com.example.StudentManagementApplication - Started StudentManagementApplication in 3.167 seconds (process running for 2771.745)
2025-07-31 00:01:42.644 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-07-31 00:03:26.403 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-07-31 00:03:26.404 [Thread-194] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-31 00:03:26.405 [tomcat-shutdown] INFO  org.apache.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8080"]
2025-07-31 00:03:26.411 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
2025-07-31 00:03:26.411 [Thread-194] INFO  org.apache.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8080"]
2025-07-31 00:03:26.422 [Thread-194] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-37} closing ...
2025-07-31 00:03:26.424 [Thread-194] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-37} closed
2025-07-31 00:03:26.598 [restartedMain] INFO  com.example.StudentManagementApplication - Starting StudentManagementApplication using Java 22.0.1 with PID 7196 (C:\Users\<USER>\Desktop\vite3\student-backend\target\classes started by 张 in C:\Users\<USER>\Desktop\vite3\student-backend)
2025-07-31 00:03:26.599 [restartedMain] INFO  com.example.StudentManagementApplication - The following 1 profile is active: "test"
2025-07-31 00:03:27.159 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-07-31 00:03:27.160 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-31 00:03:27.160 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-31 00:03:27.160 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-07-31 00:03:27.209 [restartedMain] INFO  o.a.c.c.ContainerBase.[Tomcat-4].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-31 00:03:27.209 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 608 ms
2025-07-31 00:03:27.713 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-38} inited
2025-07-31 00:03:29.916 [restartedMain] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-07-31 00:03:29.959 [restartedMain] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 56a26f71-321f-4fb2-93c1-1c1d467fd91b

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-31 00:03:29.962 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-07-31 00:03:30.451 [restartedMain] INFO  o.s.b.d.autoconfigure.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-31 00:03:30.489 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-07-31 00:03:30.492 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/'
2025-07-31 00:03:30.502 [restartedMain] INFO  com.example.StudentManagementApplication - Started StudentManagementApplication in 3.934 seconds (process running for 2879.603)
2025-07-31 00:03:30.503 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-07-31 00:03:50.074 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-07-31 00:03:50.242 [Thread-199] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-31 00:03:50.284 [tomcat-shutdown] INFO  org.apache.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8080"]
2025-07-31 00:03:50.513 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
2025-07-31 00:03:50.513 [Thread-199] INFO  org.apache.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8080"]
2025-07-31 00:03:50.519 [Thread-199] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-38} closing ...
2025-07-31 00:03:50.520 [Thread-199] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-38} closed
2025-07-31 00:03:50.698 [restartedMain] INFO  com.example.StudentManagementApplication - Starting StudentManagementApplication using Java 22.0.1 with PID 7196 (C:\Users\<USER>\Desktop\vite3\student-backend\target\classes started by 张 in C:\Users\<USER>\Desktop\vite3\student-backend)
2025-07-31 00:03:50.699 [restartedMain] INFO  com.example.StudentManagementApplication - The following 1 profile is active: "test"
2025-07-31 00:03:51.398 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-07-31 00:03:51.399 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-31 00:03:51.399 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-31 00:03:51.399 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-07-31 00:03:51.448 [restartedMain] INFO  o.a.c.c.ContainerBase.[Tomcat-4].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-31 00:03:51.448 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 747 ms
2025-07-31 00:03:51.956 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-39} inited
2025-07-31 00:03:53.359 [restartedMain] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-07-31 00:03:53.397 [restartedMain] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 05393ddb-dbb6-40c0-9cb7-9f1114b188c7

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-31 00:03:53.399 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-07-31 00:03:53.817 [restartedMain] INFO  o.s.b.d.autoconfigure.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-31 00:03:53.852 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-07-31 00:03:53.856 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/'
2025-07-31 00:03:53.864 [restartedMain] INFO  com.example.StudentManagementApplication - Started StudentManagementApplication in 3.202 seconds (process running for 2902.965)
2025-07-31 00:03:53.865 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-07-31 00:05:11.873 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-07-31 00:05:11.902 [Thread-204] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-31 00:05:11.902 [tomcat-shutdown] INFO  org.apache.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8080"]
2025-07-31 00:05:11.922 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
2025-07-31 00:05:11.923 [Thread-204] INFO  org.apache.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8080"]
2025-07-31 00:05:11.925 [Thread-204] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-39} closing ...
2025-07-31 00:05:11.926 [Thread-204] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-39} closed
2025-07-31 00:05:12.047 [restartedMain] INFO  com.example.StudentManagementApplication - Starting StudentManagementApplication using Java 22.0.1 with PID 7196 (C:\Users\<USER>\Desktop\vite3\student-backend\target\classes started by 张 in C:\Users\<USER>\Desktop\vite3\student-backend)
2025-07-31 00:05:12.047 [restartedMain] INFO  com.example.StudentManagementApplication - The following 1 profile is active: "test"
2025-07-31 00:05:12.817 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-07-31 00:05:12.818 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-31 00:05:12.818 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-31 00:05:12.818 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-07-31 00:05:12.867 [restartedMain] INFO  o.a.c.c.ContainerBase.[Tomcat-4].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-31 00:05:12.867 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 818 ms
2025-07-31 00:05:13.410 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-40} inited
2025-07-31 00:05:15.156 [restartedMain] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-07-31 00:05:15.188 [restartedMain] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: b76d795d-285b-4dc1-8060-7c58efea3dbc

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-31 00:05:15.188 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-07-31 00:05:15.688 [restartedMain] INFO  o.s.b.d.autoconfigure.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-31 00:05:15.728 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-07-31 00:05:15.734 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/'
2025-07-31 00:05:15.747 [restartedMain] INFO  com.example.StudentManagementApplication - Started StudentManagementApplication in 3.729 seconds (process running for 2984.849)
2025-07-31 00:05:15.749 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-07-31 00:05:36.550 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-07-31 00:05:36.552 [Thread-209] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-31 00:05:36.552 [tomcat-shutdown] INFO  org.apache.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8080"]
2025-07-31 00:05:36.559 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
2025-07-31 00:05:36.559 [Thread-209] INFO  org.apache.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8080"]
2025-07-31 00:05:36.563 [Thread-209] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-40} closing ...
2025-07-31 00:05:36.564 [Thread-209] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-40} closed
2025-07-31 00:05:36.689 [restartedMain] INFO  com.example.StudentManagementApplication - Starting StudentManagementApplication using Java 22.0.1 with PID 7196 (C:\Users\<USER>\Desktop\vite3\student-backend\target\classes started by 张 in C:\Users\<USER>\Desktop\vite3\student-backend)
2025-07-31 00:05:36.689 [restartedMain] INFO  com.example.StudentManagementApplication - The following 1 profile is active: "test"
2025-07-31 00:05:37.307 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-07-31 00:05:37.308 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-31 00:05:37.308 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-31 00:05:37.308 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-07-31 00:05:37.343 [restartedMain] INFO  o.a.c.c.ContainerBase.[Tomcat-4].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-31 00:05:37.343 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 653 ms
2025-07-31 00:05:37.739 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-41} inited
2025-07-31 00:05:39.156 [restartedMain] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-07-31 00:05:39.196 [restartedMain] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 224c9c96-4ff1-45ac-ab6d-7426287a49b0

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-31 00:05:39.198 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-07-31 00:05:39.606 [restartedMain] INFO  o.s.b.d.autoconfigure.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-31 00:05:39.641 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-07-31 00:05:39.644 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/'
2025-07-31 00:05:39.654 [restartedMain] INFO  com.example.StudentManagementApplication - Started StudentManagementApplication in 2.989 seconds (process running for 3008.756)
2025-07-31 00:05:39.656 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-07-31 00:06:12.427 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-07-31 00:06:12.428 [Thread-214] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-31 00:06:12.429 [tomcat-shutdown] INFO  org.apache.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8080"]
2025-07-31 00:06:12.432 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
2025-07-31 00:06:12.432 [Thread-214] INFO  org.apache.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8080"]
2025-07-31 00:06:12.436 [Thread-214] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-41} closing ...
2025-07-31 00:06:12.437 [Thread-214] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-41} closed
2025-07-31 00:06:12.597 [restartedMain] INFO  com.example.StudentManagementApplication - Starting StudentManagementApplication using Java 22.0.1 with PID 7196 (C:\Users\<USER>\Desktop\vite3\student-backend\target\classes started by 张 in C:\Users\<USER>\Desktop\vite3\student-backend)
2025-07-31 00:06:12.597 [restartedMain] INFO  com.example.StudentManagementApplication - The following 1 profile is active: "test"
2025-07-31 00:06:13.093 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-07-31 00:06:13.094 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-31 00:06:13.094 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-31 00:06:13.094 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-07-31 00:06:13.136 [restartedMain] INFO  o.a.c.c.ContainerBase.[Tomcat-4].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-31 00:06:13.137 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 537 ms
2025-07-31 00:06:13.590 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-42} inited
2025-07-31 00:06:15.131 [restartedMain] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-07-31 00:06:15.178 [restartedMain] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 253127ad-8474-4280-acfc-2e75b4e6a643

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-31 00:06:15.182 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-07-31 00:06:15.631 [restartedMain] INFO  o.s.b.d.autoconfigure.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-31 00:06:15.665 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-07-31 00:06:15.670 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/'
2025-07-31 00:06:15.680 [restartedMain] INFO  com.example.StudentManagementApplication - Started StudentManagementApplication in 3.112 seconds (process running for 3044.783)
2025-07-31 00:06:15.683 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-07-31 00:06:53.192 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 1 class path change (0 additions, 1 deletion, 0 modifications)
2025-07-31 00:06:53.193 [Thread-219] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-31 00:06:53.194 [tomcat-shutdown] INFO  org.apache.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8080"]
2025-07-31 00:06:53.200 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
2025-07-31 00:06:53.201 [Thread-219] INFO  org.apache.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8080"]
2025-07-31 00:06:53.209 [Thread-219] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-42} closing ...
2025-07-31 00:06:53.210 [Thread-219] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-42} closed
2025-07-31 00:06:53.342 [restartedMain] INFO  com.example.StudentManagementApplication - Starting StudentManagementApplication using Java 22.0.1 with PID 7196 (C:\Users\<USER>\Desktop\vite3\student-backend\target\classes started by 张 in C:\Users\<USER>\Desktop\vite3\student-backend)
2025-07-31 00:06:53.343 [restartedMain] INFO  com.example.StudentManagementApplication - The following 1 profile is active: "test"
2025-07-31 00:06:53.967 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-07-31 00:06:53.968 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-31 00:06:53.968 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-31 00:06:53.968 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-07-31 00:06:54.015 [restartedMain] INFO  o.a.c.c.ContainerBase.[Tomcat-4].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-31 00:06:54.015 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 670 ms
2025-07-31 00:06:54.484 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-43} inited
2025-07-31 00:06:54.854 [restartedMain] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'finalGradeController' defined in file [C:\Users\<USER>\Desktop\vite3\student-backend\target\classes\com\example\controller\score\FinalGradeController.class]: Unsatisfied dependency expressed through constructor parameter 0: No qualifying bean of type 'com.example.service.score.FinalGradeService' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
2025-07-31 00:06:54.854 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-43} closing ...
2025-07-31 00:06:54.854 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-43} closed
2025-07-31 00:06:54.854 [restartedMain] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-31 00:06:54.871 [restartedMain] INFO  o.s.b.a.logging.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-07-31 00:06:54.874 [restartedMain] ERROR o.s.b.diagnostics.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Parameter 0 of constructor in com.example.controller.score.FinalGradeController required a bean of type 'com.example.service.score.FinalGradeService' that could not be found.


Action:

Consider defining a bean of type 'com.example.service.score.FinalGradeService' in your configuration.

2025-07-31 00:06:56.833 [restartedMain] INFO  com.example.StudentManagementApplication - Starting StudentManagementApplication using Java 22.0.1 with PID 7196 (C:\Users\<USER>\Desktop\vite3\student-backend\target\classes started by 张 in C:\Users\<USER>\Desktop\vite3\student-backend)
2025-07-31 00:06:56.834 [restartedMain] INFO  com.example.StudentManagementApplication - The following 1 profile is active: "test"
2025-07-31 00:06:57.154 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-07-31 00:06:57.154 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-31 00:06:57.154 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-31 00:06:57.154 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-07-31 00:06:57.174 [restartedMain] INFO  o.a.c.c.ContainerBase.[Tomcat-5].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-31 00:06:57.174 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 339 ms
2025-07-31 00:06:57.440 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-44} inited
2025-07-31 00:06:57.679 [restartedMain] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'finalGradeController' defined in file [C:\Users\<USER>\Desktop\vite3\student-backend\target\classes\com\example\controller\score\FinalGradeController.class]: Unsatisfied dependency expressed through constructor parameter 0: No qualifying bean of type 'com.example.service.score.FinalGradeService' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
2025-07-31 00:06:57.679 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-44} closing ...
2025-07-31 00:06:57.680 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-44} closed
2025-07-31 00:06:57.681 [restartedMain] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-31 00:06:57.684 [restartedMain] INFO  o.s.b.a.logging.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-07-31 00:06:57.686 [restartedMain] ERROR o.s.b.diagnostics.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Parameter 0 of constructor in com.example.controller.score.FinalGradeController required a bean of type 'com.example.service.score.FinalGradeService' that could not be found.


Action:

Consider defining a bean of type 'com.example.service.score.FinalGradeService' in your configuration.

2025-07-31 00:07:16.902 [restartedMain] INFO  com.example.StudentManagementApplication - Starting StudentManagementApplication using Java 22.0.1 with PID 7196 (C:\Users\<USER>\Desktop\vite3\student-backend\target\classes started by 张 in C:\Users\<USER>\Desktop\vite3\student-backend)
2025-07-31 00:07:16.902 [restartedMain] INFO  com.example.StudentManagementApplication - The following 1 profile is active: "test"
2025-07-31 00:07:17.186 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-07-31 00:07:17.186 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-31 00:07:17.186 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-31 00:07:17.186 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-07-31 00:07:17.207 [restartedMain] INFO  o.a.c.c.ContainerBase.[Tomcat-6].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-31 00:07:17.207 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 305 ms
2025-07-31 00:07:17.493 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-45} inited
2025-07-31 00:07:17.834 [restartedMain] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'finalGradeController' defined in file [C:\Users\<USER>\Desktop\vite3\student-backend\target\classes\com\example\controller\score\FinalGradeController.class]: Unsatisfied dependency expressed through constructor parameter 0: No qualifying bean of type 'com.example.service.score.FinalGradeService' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
2025-07-31 00:07:17.834 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-45} closing ...
2025-07-31 00:07:17.834 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-45} closed
2025-07-31 00:07:17.834 [restartedMain] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-31 00:07:17.841 [restartedMain] INFO  o.s.b.a.logging.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-07-31 00:07:17.841 [restartedMain] ERROR o.s.b.diagnostics.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Parameter 0 of constructor in com.example.controller.score.FinalGradeController required a bean of type 'com.example.service.score.FinalGradeService' that could not be found.


Action:

Consider defining a bean of type 'com.example.service.score.FinalGradeService' in your configuration.

2025-07-31 00:07:33.988 [restartedMain] INFO  com.example.StudentManagementApplication - Starting StudentManagementApplication using Java 22.0.1 with PID 7196 (C:\Users\<USER>\Desktop\vite3\student-backend\target\classes started by 张 in C:\Users\<USER>\Desktop\vite3\student-backend)
2025-07-31 00:07:33.988 [restartedMain] INFO  com.example.StudentManagementApplication - The following 1 profile is active: "test"
2025-07-31 00:07:34.269 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-07-31 00:07:34.270 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-31 00:07:34.270 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-31 00:07:34.270 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-07-31 00:07:34.291 [restartedMain] INFO  o.a.c.c.ContainerBase.[Tomcat-7].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-31 00:07:34.291 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 302 ms
2025-07-31 00:07:34.561 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-46} inited
2025-07-31 00:07:34.850 [restartedMain] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'finalGradeController' defined in file [C:\Users\<USER>\Desktop\vite3\student-backend\target\classes\com\example\controller\score\FinalGradeController.class]: Unsatisfied dependency expressed through constructor parameter 0: No qualifying bean of type 'com.example.service.score.FinalGradeService' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
2025-07-31 00:07:34.850 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-46} closing ...
2025-07-31 00:07:34.850 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-46} closed
2025-07-31 00:07:34.850 [restartedMain] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-31 00:07:34.856 [restartedMain] INFO  o.s.b.a.logging.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-07-31 00:07:34.857 [restartedMain] ERROR o.s.b.diagnostics.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Parameter 0 of constructor in com.example.controller.score.FinalGradeController required a bean of type 'com.example.service.score.FinalGradeService' that could not be found.


Action:

Consider defining a bean of type 'com.example.service.score.FinalGradeService' in your configuration.

2025-07-31 00:08:06.632 [restartedMain] INFO  com.example.StudentManagementApplication - Starting StudentManagementApplication using Java 22.0.1 with PID 7196 (C:\Users\<USER>\Desktop\vite3\student-backend\target\classes started by 张 in C:\Users\<USER>\Desktop\vite3\student-backend)
2025-07-31 00:08:06.632 [restartedMain] INFO  com.example.StudentManagementApplication - The following 1 profile is active: "test"
2025-07-31 00:08:06.942 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-07-31 00:08:06.943 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-31 00:08:06.943 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-31 00:08:06.943 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-07-31 00:08:06.964 [restartedMain] INFO  o.a.c.c.ContainerBase.[Tomcat-8].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-31 00:08:06.964 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 331 ms
2025-07-31 00:08:07.228 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-47} inited
2025-07-31 00:08:07.549 [restartedMain] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'finalGradeController' defined in file [C:\Users\<USER>\Desktop\vite3\student-backend\target\classes\com\example\controller\score\FinalGradeController.class]: Unsatisfied dependency expressed through constructor parameter 0: No qualifying bean of type 'com.example.service.score.FinalGradeService' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
2025-07-31 00:08:07.550 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-47} closing ...
2025-07-31 00:08:07.550 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-47} closed
2025-07-31 00:08:07.551 [restartedMain] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-31 00:08:07.555 [restartedMain] INFO  o.s.b.a.logging.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-07-31 00:08:07.555 [restartedMain] ERROR o.s.b.diagnostics.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Parameter 0 of constructor in com.example.controller.score.FinalGradeController required a bean of type 'com.example.service.score.FinalGradeService' that could not be found.


Action:

Consider defining a bean of type 'com.example.service.score.FinalGradeService' in your configuration.

2025-07-31 00:08:28.971 [restartedMain] INFO  com.example.StudentManagementApplication - Starting StudentManagementApplication using Java 22.0.1 with PID 7196 (C:\Users\<USER>\Desktop\vite3\student-backend\target\classes started by 张 in C:\Users\<USER>\Desktop\vite3\student-backend)
2025-07-31 00:08:28.971 [restartedMain] INFO  com.example.StudentManagementApplication - The following 1 profile is active: "test"
2025-07-31 00:08:29.255 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-07-31 00:08:29.255 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-31 00:08:29.255 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-31 00:08:29.255 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-07-31 00:08:29.276 [restartedMain] INFO  o.a.c.c.ContainerBase.[Tomcat-9].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-31 00:08:29.276 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 305 ms
2025-07-31 00:08:29.562 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-48} inited
2025-07-31 00:08:29.864 [restartedMain] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'finalGradeController' defined in file [C:\Users\<USER>\Desktop\vite3\student-backend\target\classes\com\example\controller\score\FinalGradeController.class]: Unsatisfied dependency expressed through constructor parameter 0: No qualifying bean of type 'com.example.service.score.FinalGradeService' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
2025-07-31 00:08:29.864 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-48} closing ...
2025-07-31 00:08:29.865 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-48} closed
2025-07-31 00:08:29.866 [restartedMain] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-31 00:08:29.869 [restartedMain] INFO  o.s.b.a.logging.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-07-31 00:08:29.871 [restartedMain] ERROR o.s.b.diagnostics.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Parameter 0 of constructor in com.example.controller.score.FinalGradeController required a bean of type 'com.example.service.score.FinalGradeService' that could not be found.


Action:

Consider defining a bean of type 'com.example.service.score.FinalGradeService' in your configuration.

2025-07-31 00:09:29.871 [restartedMain] INFO  com.example.StudentManagementApplication - Starting StudentManagementApplication using Java 22.0.1 with PID 7196 (C:\Users\<USER>\Desktop\vite3\student-backend\target\classes started by 张 in C:\Users\<USER>\Desktop\vite3\student-backend)
2025-07-31 00:09:29.872 [restartedMain] INFO  com.example.StudentManagementApplication - The following 1 profile is active: "test"
2025-07-31 00:09:30.412 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-07-31 00:09:30.412 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-31 00:09:30.413 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-31 00:09:30.413 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-07-31 00:09:30.441 [restartedMain] INFO  o.a.c.c.ContainerBase.[Tomcat-10].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-31 00:09:30.441 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 567 ms
2025-07-31 00:09:30.724 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-49} inited
2025-07-31 00:09:31.070 [restartedMain] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'finalGradeController' defined in file [C:\Users\<USER>\Desktop\vite3\student-backend\target\classes\com\example\controller\score\FinalGradeController.class]: Unsatisfied dependency expressed through constructor parameter 0: No qualifying bean of type 'com.example.service.score.FinalGradeService' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
2025-07-31 00:09:31.070 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-49} closing ...
2025-07-31 00:09:31.071 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-49} closed
2025-07-31 00:09:31.071 [restartedMain] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-31 00:09:31.077 [restartedMain] INFO  o.s.b.a.logging.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-07-31 00:09:31.080 [restartedMain] ERROR o.s.b.diagnostics.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Parameter 0 of constructor in com.example.controller.score.FinalGradeController required a bean of type 'com.example.service.score.FinalGradeService' that could not be found.


Action:

Consider defining a bean of type 'com.example.service.score.FinalGradeService' in your configuration.

2025-07-31 00:10:16.332 [restartedMain] INFO  com.example.StudentManagementApplication - Starting StudentManagementApplication using Java 22.0.1 with PID 7196 (C:\Users\<USER>\Desktop\vite3\student-backend\target\classes started by 张 in C:\Users\<USER>\Desktop\vite3\student-backend)
2025-07-31 00:10:16.332 [restartedMain] INFO  com.example.StudentManagementApplication - The following 1 profile is active: "test"
2025-07-31 00:10:16.640 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-07-31 00:10:16.640 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-31 00:10:16.640 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-31 00:10:16.640 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-07-31 00:10:16.660 [restartedMain] INFO  o.a.c.c.ContainerBase.[Tomcat-11].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-31 00:10:16.660 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 328 ms
2025-07-31 00:10:16.887 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-50} inited
2025-07-31 00:10:17.169 [restartedMain] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'finalGradeController' defined in file [C:\Users\<USER>\Desktop\vite3\student-backend\target\classes\com\example\controller\score\FinalGradeController.class]: Unsatisfied dependency expressed through constructor parameter 0: No qualifying bean of type 'com.example.service.score.FinalGradeService' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
2025-07-31 00:10:17.169 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-50} closing ...
2025-07-31 00:10:17.170 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-50} closed
2025-07-31 00:10:17.170 [restartedMain] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-31 00:10:17.175 [restartedMain] INFO  o.s.b.a.logging.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-07-31 00:10:17.177 [restartedMain] ERROR o.s.b.diagnostics.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Parameter 0 of constructor in com.example.controller.score.FinalGradeController required a bean of type 'com.example.service.score.FinalGradeService' that could not be found.


Action:

Consider defining a bean of type 'com.example.service.score.FinalGradeService' in your configuration.

2025-07-31 00:10:40.600 [restartedMain] INFO  com.example.StudentManagementApplication - Starting StudentManagementApplication using Java 22.0.1 with PID 7196 (C:\Users\<USER>\Desktop\vite3\student-backend\target\classes started by 张 in C:\Users\<USER>\Desktop\vite3\student-backend)
2025-07-31 00:10:40.600 [restartedMain] INFO  com.example.StudentManagementApplication - The following 1 profile is active: "test"
2025-07-31 00:10:40.891 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-07-31 00:10:40.891 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-31 00:10:40.891 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-31 00:10:40.891 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-07-31 00:10:40.911 [restartedMain] INFO  o.a.c.c.ContainerBase.[Tomcat-12].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-31 00:10:40.911 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 310 ms
2025-07-31 00:10:41.199 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-51} inited
2025-07-31 00:10:41.484 [restartedMain] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'finalGradeController' defined in file [C:\Users\<USER>\Desktop\vite3\student-backend\target\classes\com\example\controller\score\FinalGradeController.class]: Unsatisfied dependency expressed through constructor parameter 0: No qualifying bean of type 'com.example.service.score.FinalGradeService' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
2025-07-31 00:10:41.485 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-51} closing ...
2025-07-31 00:10:41.485 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-51} closed
2025-07-31 00:10:41.486 [restartedMain] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-31 00:10:41.490 [restartedMain] INFO  o.s.b.a.logging.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-07-31 00:10:41.492 [restartedMain] ERROR o.s.b.diagnostics.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Parameter 0 of constructor in com.example.controller.score.FinalGradeController required a bean of type 'com.example.service.score.FinalGradeService' that could not be found.


Action:

Consider defining a bean of type 'com.example.service.score.FinalGradeService' in your configuration.

2025-07-31 00:11:10.051 [restartedMain] INFO  com.example.StudentManagementApplication - Starting StudentManagementApplication using Java 22.0.1 with PID 7196 (C:\Users\<USER>\Desktop\vite3\student-backend\target\classes started by 张 in C:\Users\<USER>\Desktop\vite3\student-backend)
2025-07-31 00:11:10.051 [restartedMain] INFO  com.example.StudentManagementApplication - The following 1 profile is active: "test"
2025-07-31 00:11:10.377 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-07-31 00:11:10.377 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-31 00:11:10.377 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-31 00:11:10.377 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-07-31 00:11:10.397 [restartedMain] INFO  o.a.c.c.ContainerBase.[Tomcat-13].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-31 00:11:10.397 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 346 ms
2025-07-31 00:11:10.645 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-52} inited
2025-07-31 00:11:10.930 [restartedMain] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'finalGradeController' defined in file [C:\Users\<USER>\Desktop\vite3\student-backend\target\classes\com\example\controller\score\FinalGradeController.class]: Unsatisfied dependency expressed through constructor parameter 0: No qualifying bean of type 'com.example.service.score.FinalGradeService' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
2025-07-31 00:11:10.930 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-52} closing ...
2025-07-31 00:11:10.931 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-52} closed
2025-07-31 00:11:10.931 [restartedMain] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-31 00:11:10.935 [restartedMain] INFO  o.s.b.a.logging.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-07-31 00:11:10.937 [restartedMain] ERROR o.s.b.diagnostics.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Parameter 0 of constructor in com.example.controller.score.FinalGradeController required a bean of type 'com.example.service.score.FinalGradeService' that could not be found.


Action:

Consider defining a bean of type 'com.example.service.score.FinalGradeService' in your configuration.

2025-07-31 00:11:31.362 [restartedMain] INFO  com.example.StudentManagementApplication - Starting StudentManagementApplication using Java 22.0.1 with PID 7196 (C:\Users\<USER>\Desktop\vite3\student-backend\target\classes started by 张 in C:\Users\<USER>\Desktop\vite3\student-backend)
2025-07-31 00:11:31.362 [restartedMain] INFO  com.example.StudentManagementApplication - The following 1 profile is active: "test"
2025-07-31 00:11:31.650 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-07-31 00:11:31.650 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-31 00:11:31.650 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-31 00:11:31.651 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-07-31 00:11:31.673 [restartedMain] INFO  o.a.c.c.ContainerBase.[Tomcat-14].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-31 00:11:31.673 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 311 ms
2025-07-31 00:11:31.915 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-53} inited
2025-07-31 00:11:32.205 [restartedMain] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'finalGradeController' defined in file [C:\Users\<USER>\Desktop\vite3\student-backend\target\classes\com\example\controller\score\FinalGradeController.class]: Unsatisfied dependency expressed through constructor parameter 0: No qualifying bean of type 'com.example.service.score.FinalGradeService' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
2025-07-31 00:11:32.205 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-53} closing ...
2025-07-31 00:11:32.205 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-53} closed
2025-07-31 00:11:32.206 [restartedMain] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-31 00:11:32.210 [restartedMain] INFO  o.s.b.a.logging.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-07-31 00:11:32.211 [restartedMain] ERROR o.s.b.diagnostics.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Parameter 0 of constructor in com.example.controller.score.FinalGradeController required a bean of type 'com.example.service.score.FinalGradeService' that could not be found.


Action:

Consider defining a bean of type 'com.example.service.score.FinalGradeService' in your configuration.

2025-07-31 00:12:26.119 [restartedMain] INFO  com.example.StudentManagementApplication - Starting StudentManagementApplication using Java 22.0.1 with PID 7196 (C:\Users\<USER>\Desktop\vite3\student-backend\target\classes started by 张 in C:\Users\<USER>\Desktop\vite3\student-backend)
2025-07-31 00:12:26.119 [restartedMain] INFO  com.example.StudentManagementApplication - The following 1 profile is active: "test"
2025-07-31 00:12:26.853 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-07-31 00:12:26.854 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-31 00:12:26.854 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-31 00:12:26.854 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-07-31 00:12:26.888 [restartedMain] INFO  o.a.c.c.ContainerBase.[Tomcat-15].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-31 00:12:26.888 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 769 ms
2025-07-31 00:12:27.221 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-54} inited
2025-07-31 00:12:27.877 [restartedMain] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-07-31 00:12:27.899 [restartedMain] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: eb55514b-572e-470a-a1db-b6117b4d43d4

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-31 00:12:27.900 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-07-31 00:12:28.128 [restartedMain] INFO  o.s.b.d.autoconfigure.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-31 00:12:28.153 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-07-31 00:12:28.153 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/'
2025-07-31 00:12:28.160 [restartedMain] INFO  com.example.StudentManagementApplication - Started StudentManagementApplication in 2.055 seconds (process running for 3417.263)
2025-07-31 00:12:28.160 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-07-31 00:12:41.116 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 1 class path change (1 addition, 0 deletions, 0 modifications)
2025-07-31 00:12:41.118 [Thread-224] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-31 00:12:41.119 [tomcat-shutdown] INFO  org.apache.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8080"]
2025-07-31 00:12:41.122 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
2025-07-31 00:12:41.122 [Thread-224] INFO  org.apache.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8080"]
2025-07-31 00:12:41.124 [Thread-224] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-54} closing ...
2025-07-31 00:12:41.125 [Thread-224] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-54} closed
2025-07-31 00:12:41.225 [restartedMain] INFO  com.example.StudentManagementApplication - Starting StudentManagementApplication using Java 22.0.1 with PID 7196 (C:\Users\<USER>\Desktop\vite3\student-backend\target\classes started by 张 in C:\Users\<USER>\Desktop\vite3\student-backend)
2025-07-31 00:12:41.225 [restartedMain] INFO  com.example.StudentManagementApplication - The following 1 profile is active: "test"
2025-07-31 00:12:41.537 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-07-31 00:12:41.537 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-31 00:12:41.537 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-31 00:12:41.537 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-07-31 00:12:41.557 [restartedMain] INFO  o.a.c.c.ContainerBase.[Tomcat-15].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-31 00:12:41.557 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 331 ms
2025-07-31 00:12:41.808 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-55} inited
2025-07-31 00:12:42.430 [restartedMain] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-07-31 00:12:42.458 [restartedMain] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: fd8474a3-cd60-4220-b25a-a5f3b13d3e57

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-31 00:12:42.459 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-07-31 00:12:42.676 [restartedMain] INFO  o.s.b.d.autoconfigure.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-31 00:12:42.697 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-07-31 00:12:42.699 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/'
2025-07-31 00:12:42.704 [restartedMain] INFO  com.example.StudentManagementApplication - Started StudentManagementApplication in 1.494 seconds (process running for 3431.806)
2025-07-31 00:12:42.705 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-07-31 00:15:58.280 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-07-31 00:15:58.282 [Thread-273] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-31 00:15:58.282 [tomcat-shutdown] INFO  org.apache.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8080"]
2025-07-31 00:15:58.287 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
2025-07-31 00:15:58.287 [Thread-273] INFO  org.apache.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8080"]
2025-07-31 00:15:58.289 [Thread-273] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-55} closing ...
2025-07-31 00:15:58.290 [Thread-273] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-55} closed
2025-07-31 00:15:58.383 [restartedMain] INFO  com.example.StudentManagementApplication - Starting StudentManagementApplication using Java 22.0.1 with PID 7196 (C:\Users\<USER>\Desktop\vite3\student-backend\target\classes started by 张 in C:\Users\<USER>\Desktop\vite3\student-backend)
2025-07-31 00:15:58.383 [restartedMain] INFO  com.example.StudentManagementApplication - The following 1 profile is active: "test"
2025-07-31 00:15:58.703 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-07-31 00:15:58.703 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-31 00:15:58.703 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-31 00:15:58.703 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-07-31 00:15:58.725 [restartedMain] INFO  o.a.c.c.ContainerBase.[Tomcat-15].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-31 00:15:58.725 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 341 ms
2025-07-31 00:15:58.984 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-56} inited
2025-07-31 00:15:59.649 [restartedMain] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-07-31 00:15:59.671 [restartedMain] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 0e3c73ea-ce07-43d2-8da4-85930ca76e3b

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-31 00:15:59.673 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-07-31 00:15:59.895 [restartedMain] INFO  o.s.b.d.autoconfigure.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-31 00:15:59.913 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-07-31 00:15:59.915 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/'
2025-07-31 00:15:59.920 [restartedMain] INFO  com.example.StudentManagementApplication - Started StudentManagementApplication in 1.553 seconds (process running for 3629.022)
2025-07-31 00:15:59.921 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
