package com.example.service.basic;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.example.entity.basic.Classes;
import com.example.dto.basic.ClassesQueryDTO;
import com.example.vo.basic.ClassesVO;
import com.example.vo.educational.CourseVO;

import java.util.List;

/**
 * 班级服务接口
 *
 * <AUTHOR>
 * @since 2025-07-27
 */
public interface ClassesService extends IService<Classes> {

    /**
     * 分页查询班级列表
     *
     * @param query 查询条件
     * @return 班级分页列表
     */
    IPage<ClassesVO> getClassesPage(ClassesQueryDTO query);

    /**
     * 根据ID查询班级详情
     *
     * @param id 班级ID
     * @return 班级详情
     */
    ClassesVO getClassesById(Integer id);

    /**
     * 查询所有班级列表（用于下拉选择）
     *
     * @return 班级列表
     */
    List<ClassesVO> getAllClasses();

    /**
     * 根据专业代码查询班级列表
     *
     * @param majorCode 专业代码
     * @return 班级列表
     */
    List<ClassesVO> getClassesByMajorCode(String majorCode);

    /**
     * 根据班主任工号查询班级列表
     *
     * @param headTeacherCode 班主任工号
     * @return 班级列表
     */
    List<ClassesVO> getClassesByHeadTeacher(String headTeacherCode);

    /**
     * 保存班级
     *
     * @param classesVO 班级信息
     */
    void saveClasses(ClassesVO classesVO);

    /**
     * 更新班级
     *
     * @param classesVO 班级信息
     */
    void updateClasses(ClassesVO classesVO);

    /**
     * 删除班级
     *
     * @param id 班级ID
     */
    void deleteClasses(Integer id);

    /**
     * 批量删除班级
     *
     * @param ids 班级ID列表
     */
    void batchDeleteClasses(List<Integer> ids);



    /**
     * 更新班级学生人数
     *
     * @param classId 班级ID
     * @param studentCount 学生人数
     */
    void updateStudentCount(Integer classId, Integer studentCount);

    /**
     * 获取班级在指定学期的课程列表
     *
     * @param classCode 班级代码
     * @param semesterId 学期ID
     * @return 课程列表
     */
    List<CourseVO> getCoursesByClassAndSemester(String classCode, Integer semesterId);
}
