package com.example.common.export.processor;

import com.example.common.export.config.ExportConfig;
import com.example.common.export.config.ImportConfig;

import java.util.List;
import java.util.Map;

/**
 * 数据处理器接口
 * 用于处理不同类型的数据转换逻辑
 *
 * @param <T> 原始数据类型
 * @param <R> 处理结果类型
 * <AUTHOR>
 * @since 2025-01-30
 */
public interface DataProcessor<T, R> {

    /**
     * 处理导出数据
     * 将原始数据转换为导出格式
     *
     * @param data   原始数据列表
     * @param config 导出配置
     * @return 转换后的导出数据
     */
    List<Map<String, Object>> processExportData(List<T> data, ExportConfig config);

    /**
     * 处理导入数据
     * 将Excel数据转换为业务对象
     *
     * @param data   Excel数据列表
     * @param config 导入配置
     * @return 转换后的业务对象列表
     */
    List<R> processImportData(List<Map<String, Object>> data, ImportConfig config);

    /**
     * 验证导入数据
     * 验证数据的有效性和完整性
     *
     * @param data   待验证的数据
     * @param config 导入配置
     * @return 验证结果
     */
    ValidationResult validateImportData(R data, ImportConfig config);

    /**
     * 获取支持的数据类型
     */
    Class<T> getSupportedType();

    /**
     * 验证结果类
     */
    class ValidationResult {
        private boolean valid;
        private String errorMessage;
        private int rowNumber;

        public ValidationResult(boolean valid, String errorMessage, int rowNumber) {
            this.valid = valid;
            this.errorMessage = errorMessage;
            this.rowNumber = rowNumber;
        }

        public static ValidationResult success() {
            return new ValidationResult(true, null, 0);
        }

        public static ValidationResult error(String message, int rowNumber) {
            return new ValidationResult(false, message, rowNumber);
        }

        // Getters
        public boolean isValid() {
            return valid;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public int getRowNumber() {
            return rowNumber;
        }
    }
}
