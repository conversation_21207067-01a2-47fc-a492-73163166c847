package com.example.common.export.service;

import com.example.common.export.ApachePoiExportService;
import com.example.common.export.config.ExportConfig;
import com.example.common.export.config.ImportConfig;
import com.example.common.export.processor.DataProcessor;
import com.example.dto.score.GradeImportResultDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;
import java.util.ArrayList;

/**
 * 抽象导出导入服务基类
 * 实现通用的导出导入逻辑，使用模板方法模式
 *
 * @param <T> 导出数据类型
 * @param <R> 导入结果类型
 * <AUTHOR>
 * @since 2025-01-30
 */
@Slf4j
public abstract class AbstractExportImportService<T, R> implements ExportImportService<T, R> {

    @Autowired
    protected ApachePoiExportService apachePoiExportService;

    /**
     * 获取数据处理器
     * 子类需要实现此方法来提供具体的数据处理器
     */
    protected abstract DataProcessor<T, R> getDataProcessor();

    @Override
    public byte[] exportToExcel(List<T> data, ExportConfig config) {
        log.info("开始导出数据到Excel，数据量: {}, 配置: {}", data.size(), config.getSheetName());
        
        try {
            // 验证配置
            validateExportConfig(config);
            
            // 验证数据量
            if (data.size() > config.getMaxRows()) {
                throw new IllegalArgumentException("导出数据量超过限制: " + config.getMaxRows());
            }
            
            // 使用数据处理器转换数据
            DataProcessor<T, R> processor = getDataProcessor();
            List<Map<String, Object>> exportData = processor.processExportData(data, config);
            
            // 使用Apache POI导出
            byte[] result = apachePoiExportService.exportGradesToExcel(
                exportData, config.getHeaders(), config.getSheetName());
            
            log.info("数据导出成功，文件大小: {} bytes", result.length);
            return result;
            
        } catch (Exception e) {
            log.error("导出数据失败: {}", e.getMessage(), e);
            throw new RuntimeException("导出数据失败: " + e.getMessage(), e);
        }
    }

    @Override
    public byte[] exportToExcel(Map<String, Object> queryParams, ExportConfig config) {
        log.info("开始根据查询参数导出数据，参数: {}", queryParams);
        
        try {
            // 获取导出数据
            List<T> data = getExportData(queryParams);
            
            // 调用基础导出方法
            return exportToExcel(data, config);
            
        } catch (Exception e) {
            log.error("根据查询参数导出数据失败: {}", e.getMessage(), e);
            throw new RuntimeException("导出数据失败: " + e.getMessage(), e);
        }
    }

    @Override
    public byte[] generateImportTemplate(ImportConfig config) {
        log.info("开始生成导入模板，配置: {}", config.getSheetName());
        
        try {
            // 验证配置
            validateImportConfig(config);
            
            // 使用Apache POI生成模板
            byte[] result = apachePoiExportService.generateTemplate(
                config.getHeaders(), config.getSheetName());
            
            log.info("导入模板生成成功，文件大小: {} bytes", result.length);
            return result;
            
        } catch (Exception e) {
            log.error("生成导入模板失败: {}", e.getMessage(), e);
            throw new RuntimeException("生成导入模板失败: " + e.getMessage(), e);
        }
    }

    @Override
    public GradeImportResultDTO importFromExcel(MultipartFile file, ImportConfig config) {
        log.info("开始导入Excel数据，文件: {}, 大小: {} bytes", 
                file.getOriginalFilename(), file.getSize());
        
        GradeImportResultDTO result = new GradeImportResultDTO();
        
        try {
            // 验证配置
            validateImportConfig(config);
            
            // 验证文件
            validateImportFile(file, config);
            
            // 使用Apache POI读取Excel数据
            List<Map<String, Object>> excelData = apachePoiExportService.importExcelData(
                file, config.getHeaders(), config.getSheetName());
            
            if (excelData.isEmpty()) {
                result.setSuccess(false);
                result.addErrorMessage("Excel文件中没有有效数据");
                return result;
            }
            
            result.setTotalRows(excelData.size());
            
            // 使用数据处理器转换数据
            DataProcessor<T, R> processor = getDataProcessor();
            List<R> importData = processor.processImportData(excelData, config);
            
            // 验证数据
            List<R> validData = validateAndFilterData(importData, processor, config, result);
            
            // 保存有效数据
            if (!validData.isEmpty()) {
                int successCount = saveImportData(validData);
                result.setSuccessRows(successCount);
            }
            
            result.setFailedRows(result.getTotalRows() - result.getSuccessRows());
            result.setSuccess(result.getSuccessRows() > 0);
            
            log.info("Excel数据导入完成，总行数: {}, 成功: {}, 失败: {}", 
                    result.getTotalRows(), result.getSuccessRows(), result.getFailedRows());
            
            return result;
            
        } catch (Exception e) {
            log.error("导入Excel数据失败: {}", e.getMessage(), e);
            result.setSuccess(false);
            result.addErrorMessage("导入失败: " + e.getMessage());
            return result;
        }
    }

    @Override
    public GradeImportResultDTO validateImportData(MultipartFile file, ImportConfig config) {
        log.info("开始验证导入数据，文件: {}", file.getOriginalFilename());
        
        GradeImportResultDTO result = new GradeImportResultDTO();
        
        try {
            // 验证配置
            validateImportConfig(config);
            
            // 验证文件
            validateImportFile(file, config);
            
            // 使用Apache POI读取Excel数据
            List<Map<String, Object>> excelData = apachePoiExportService.importExcelData(
                file, config.getHeaders(), config.getSheetName());
            
            if (excelData.isEmpty()) {
                result.setSuccess(false);
                result.addErrorMessage("Excel文件中没有有效数据");
                return result;
            }
            
            result.setTotalRows(excelData.size());
            
            // 使用数据处理器转换数据
            DataProcessor<T, R> processor = getDataProcessor();
            List<R> importData = processor.processImportData(excelData, config);
            
            // 仅验证数据，不保存
            validateAndFilterData(importData, processor, config, result);
            
            result.setSuccess(result.getErrorMessages().isEmpty());
            
            log.info("导入数据验证完成，总行数: {}, 错误数: {}", 
                    result.getTotalRows(), result.getErrorMessages().size());
            
            return result;
            
        } catch (Exception e) {
            log.error("验证导入数据失败: {}", e.getMessage(), e);
            result.setSuccess(false);
            result.addErrorMessage("验证失败: " + e.getMessage());
            return result;
        }
    }

    /**
     * 验证导出配置
     */
    protected void validateExportConfig(ExportConfig config) {
        if (config == null) {
            throw new IllegalArgumentException("导出配置不能为空");
        }
        if (!config.isValid()) {
            throw new IllegalArgumentException("导出配置无效");
        }
    }

    /**
     * 验证导入配置
     */
    protected void validateImportConfig(ImportConfig config) {
        if (config == null) {
            throw new IllegalArgumentException("导入配置不能为空");
        }
        if (!config.isValid()) {
            throw new IllegalArgumentException("导入配置无效");
        }
    }

    /**
     * 验证导入文件
     */
    protected void validateImportFile(MultipartFile file, ImportConfig config) {
        if (file == null || file.isEmpty()) {
            throw new IllegalArgumentException("文件不能为空");
        }
        
        if (file.getSize() > config.getMaxFileSize()) {
            throw new IllegalArgumentException("文件大小超过限制: " + config.getMaxFileSize());
        }
        
        String fileName = file.getOriginalFilename();
        if (fileName == null || (!fileName.endsWith(".xlsx") && !fileName.endsWith(".xls"))) {
            throw new IllegalArgumentException("请上传Excel文件（.xlsx或.xls格式）");
        }
    }

    /**
     * 验证并过滤数据
     */
    protected List<R> validateAndFilterData(List<R> data, DataProcessor<T, R> processor, 
                                          ImportConfig config, GradeImportResultDTO result) {
        List<R> validData = new ArrayList<>();
        
        for (R item : data) {
            DataProcessor.ValidationResult validation = processor.validateImportData(item, config);
            if (validation.isValid()) {
                validData.add(item);
            } else {
                result.addErrorMessage("第" + validation.getRowNumber() + "行: " + validation.getErrorMessage());
            }
        }
        
        return validData;
    }
}
