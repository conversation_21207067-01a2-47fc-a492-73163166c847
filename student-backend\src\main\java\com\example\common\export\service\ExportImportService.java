package com.example.common.export.service;

import com.example.common.export.config.ExportConfig;
import com.example.common.export.config.ImportConfig;
import com.example.dto.score.GradeImportResultDTO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 导出导入服务接口
 * 定义通用的导出导入方法
 *
 * @param <T> 导出数据类型
 * @param <R> 导入结果类型
 * <AUTHOR>
 * @since 2025-01-30
 */
public interface ExportImportService<T, R> {

    /**
     * 导出数据到Excel
     *
     * @param data   导出数据列表
     * @param config 导出配置
     * @return Excel文件字节数组
     */
    byte[] exportToExcel(List<T> data, ExportConfig config);

    /**
     * 导出数据到Excel（带查询参数）
     *
     * @param queryParams 查询参数
     * @param config      导出配置
     * @return Excel文件字节数组
     */
    byte[] exportToExcel(Map<String, Object> queryParams, ExportConfig config);

    /**
     * 生成导入模板
     *
     * @param config 导入配置
     * @return Excel模板文件字节数组
     */
    byte[] generateImportTemplate(ImportConfig config);

    /**
     * 导入Excel数据
     *
     * @param file   上传的Excel文件
     * @param config 导入配置
     * @return 导入结果
     */
    GradeImportResultDTO importFromExcel(MultipartFile file, ImportConfig config);

    /**
     * 验证导入数据
     *
     * @param file   上传的Excel文件
     * @param config 导入配置
     * @return 验证结果
     */
    GradeImportResultDTO validateImportData(MultipartFile file, ImportConfig config);

    /**
     * 获取导出数据
     * 子类需要实现此方法来获取具体的导出数据
     *
     * @param queryParams 查询参数
     * @return 导出数据列表
     */
    List<T> getExportData(Map<String, Object> queryParams);

    /**
     * 保存导入数据
     * 子类需要实现此方法来保存具体的导入数据
     *
     * @param data 导入数据列表
     * @return 保存成功的数量
     */
    int saveImportData(List<R> data);

    /**
     * 获取支持的导出数据类型
     */
    Class<T> getExportDataType();

    /**
     * 获取支持的导入结果类型
     */
    Class<R> getImportResultType();
}
