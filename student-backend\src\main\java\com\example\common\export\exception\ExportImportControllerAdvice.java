package com.example.common.export.exception;

import com.example.common.Result;
import com.example.dto.score.GradeImportResultDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.multipart.MaxUploadSizeExceededException;

import java.io.FileNotFoundException;
import java.io.IOException;

/**
 * 导出导入统一异常处理
 * 专门处理导出导入相关的异常情况
 *
 * <AUTHOR>
 * @since 2025-01-30
 */
@RestControllerAdvice
@Slf4j
public class ExportImportControllerAdvice {

    /**
     * 处理文件上传大小超限异常
     */
    @ExceptionHandler(MaxUploadSizeExceededException.class)
    public Result<GradeImportResultDTO> handleMaxUploadSizeExceeded(MaxUploadSizeExceededException e) {
        log.warn("文件上传大小超限: {}", e.getMessage());
        
        GradeImportResultDTO result = new GradeImportResultDTO();
        result.setSuccess(false);
        result.addErrorMessage("文件大小超过限制，请上传小于10MB的文件");
        
        return Result.result(false, Result.BAD_REQUEST, "文件大小超限", result);
    }

    /**
     * 处理文件未找到异常
     */
    @ExceptionHandler(FileNotFoundException.class)
    public Result<String> handleFileNotFound(FileNotFoundException e) {
        log.warn("文件未找到: {}", e.getMessage());
        return Result.result(false, Result.NOT_FOUND, "文件未找到", null);
    }

    /**
     * 处理IO异常
     */
    @ExceptionHandler(IOException.class)
    public Result<String> handleIOException(IOException e) {
        log.error("文件操作异常: {}", e.getMessage(), e);
        return Result.result(false, Result.INTERNAL_SERVER_ERROR, "文件操作失败: " + e.getMessage(), null);
    }

    /**
     * 处理导出异常
     */
    @ExceptionHandler(ExportException.class)
    public ResponseEntity<Resource> handleExportException(ExportException e) {
        log.error("导出异常: {}", e.getMessage(), e);
        return ResponseEntity.internalServerError().build();
    }

    /**
     * 处理导入异常
     */
    @ExceptionHandler(ImportException.class)
    public Result<GradeImportResultDTO> handleImportException(ImportException e) {
        log.error("导入异常: {}", e.getMessage(), e);
        
        GradeImportResultDTO result = new GradeImportResultDTO();
        result.setSuccess(false);
        result.addErrorMessage("导入失败: " + e.getMessage());
        
        return Result.result(false, Result.INTERNAL_SERVER_ERROR, "导入失败", result);
    }

    /**
     * 处理Excel格式异常
     */
    @ExceptionHandler(ExcelFormatException.class)
    public Result<GradeImportResultDTO> handleExcelFormatException(ExcelFormatException e) {
        log.warn("Excel格式异常: {}", e.getMessage());
        
        GradeImportResultDTO result = new GradeImportResultDTO();
        result.setSuccess(false);
        result.addErrorMessage("Excel格式错误: " + e.getMessage());
        
        return Result.result(false, Result.BAD_REQUEST, "Excel格式错误", result);
    }

    /**
     * 处理数据验证异常
     */
    @ExceptionHandler(DataValidationException.class)
    public Result<GradeImportResultDTO> handleDataValidationException(DataValidationException e) {
        log.warn("数据验证异常: {}", e.getMessage());
        
        GradeImportResultDTO result = new GradeImportResultDTO();
        result.setSuccess(false);
        result.addErrorMessage("数据验证失败: " + e.getMessage());
        
        return Result.result(false, Result.BAD_REQUEST, "数据验证失败", result);
    }

    /**
     * 处理模板生成异常
     */
    @ExceptionHandler(TemplateGenerationException.class)
    public ResponseEntity<Resource> handleTemplateGenerationException(TemplateGenerationException e) {
        log.error("模板生成异常: {}", e.getMessage(), e);
        return ResponseEntity.internalServerError().build();
    }

    /**
     * 处理通用的运行时异常（导出导入相关）
     */
    @ExceptionHandler(RuntimeException.class)
    public Result<String> handleRuntimeException(RuntimeException e) {
        String message = e.getMessage();
        
        // 判断是否为导出导入相关异常
        if (message != null && (message.contains("导出") || message.contains("导入") || 
                               message.contains("Excel") || message.contains("模板"))) {
            log.error("导出导入运行时异常: {}", message, e);
            
            if (message.contains("导出")) {
                return Result.result(false, Result.INTERNAL_SERVER_ERROR, "导出失败: " + message, null);
            } else if (message.contains("导入")) {
                return Result.result(false, Result.INTERNAL_SERVER_ERROR, "导入失败: " + message, null);
            } else {
                return Result.result(false, Result.INTERNAL_SERVER_ERROR, "操作失败: " + message, null);
            }
        }
        
        // 不是导出导入相关异常，不处理
        throw e;
    }

    /**
     * 自定义导出异常
     */
    public static class ExportException extends RuntimeException {
        public ExportException(String message) {
            super(message);
        }
        
        public ExportException(String message, Throwable cause) {
            super(message, cause);
        }
    }

    /**
     * 自定义导入异常
     */
    public static class ImportException extends RuntimeException {
        public ImportException(String message) {
            super(message);
        }
        
        public ImportException(String message, Throwable cause) {
            super(message, cause);
        }
    }

    /**
     * 自定义Excel格式异常
     */
    public static class ExcelFormatException extends RuntimeException {
        public ExcelFormatException(String message) {
            super(message);
        }
        
        public ExcelFormatException(String message, Throwable cause) {
            super(message, cause);
        }
    }

    /**
     * 自定义数据验证异常
     */
    public static class DataValidationException extends RuntimeException {
        public DataValidationException(String message) {
            super(message);
        }
        
        public DataValidationException(String message, Throwable cause) {
            super(message, cause);
        }
    }

    /**
     * 自定义模板生成异常
     */
    public static class TemplateGenerationException extends RuntimeException {
        public TemplateGenerationException(String message) {
            super(message);
        }
        
        public TemplateGenerationException(String message, Throwable cause) {
            super(message, cause);
        }
    }
}
