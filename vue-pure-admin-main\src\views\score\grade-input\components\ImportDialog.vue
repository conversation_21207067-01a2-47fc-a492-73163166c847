<template>
  <el-dialog
    v-model="visible"
    title="导入成绩"
    width="600px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <div class="import-content">
      <!-- 操作说明 -->
      <el-alert
        title="导入说明"
        type="info"
        :closable="false"
        class="mb-4"
      >
        <template #default>
          <p>1. 请先下载模板文件，按照模板格式填写成绩数据</p>
          <p>2. 支持的文件格式：.xlsx、.xls</p>
          <p>3. 文件大小不能超过10MB</p>
          <p>4. 学号、课程代码、学期ID、期末成绩为必填项</p>
          <p>5. 期末成绩范围：0-100分</p>
        </template>
      </el-alert>

      <!-- 下载模板 -->
      <div class="mb-4">
        <el-button
          type="primary"
          @click="handleDownloadTemplate"
          :loading="downloadLoading"
        >
          下载导入模板
        </el-button>
      </div>

      <!-- 文件上传 -->
      <el-upload
        ref="uploadRef"
        v-model:file-list="fileList"
        class="upload-demo"
        drag
        :auto-upload="false"
        :limit="1"
        :accept="'.xlsx,.xls'"
        :on-change="handleFileChange"
        :on-exceed="handleExceed"
        :before-upload="beforeUpload"
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">
          将文件拖到此处，或<em>点击上传</em>
        </div>
        <template #tip>
          <div class="el-upload__tip">
            只能上传 .xlsx/.xls 文件，且不超过 10MB
          </div>
        </template>
      </el-upload>

      <!-- 导入结果 -->
      <div v-if="importResult" class="mt-4">
        <el-alert
          :title="importResult.success ? '导入成功' : '导入失败'"
          :type="importResult.success ? 'success' : 'error'"
          :closable="false"
          class="mb-2"
        >
          <template #default>
            <p>{{ getSummaryText() }}</p>
          </template>
        </el-alert>

        <!-- 错误详情 -->
        <div v-if="importResult.errorMessages.length > 0" class="error-details">
          <el-collapse>
            <el-collapse-item title="查看错误详情" name="errors">
              <div class="error-list">
                <div
                  v-for="(error, index) in importResult.errorMessages"
                  :key="index"
                  class="error-item"
                >
                  {{ error }}
                </div>
              </div>
            </el-collapse-item>
          </el-collapse>
        </div>
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          @click="handleImport"
          :loading="importLoading"
          :disabled="!selectedFile"
        >
          开始导入
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { UploadFilled } from "@element-plus/icons-vue";
import type { UploadFile, UploadFiles, UploadInstance } from "element-plus";
import { importGrades, downloadImportTemplate, type GradeImportResultDTO } from "@/api/score/grade-input";
import { handleFileDownload, generateDefaultFileName } from "@/utils/fileUtils";

defineOptions({
  name: "ImportDialog"
});

// Props
interface Props {
  modelValue: boolean;
  classCode: string;
  className: string;
  courseCode: string;
  courseName: string;
  semesterId: number;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  classCode: "",
  className: "",
  courseCode: "",
  courseName: "",
  semesterId: 0
});

// Emits
const emit = defineEmits<{
  "update:modelValue": [value: boolean];
  success: [];
}>();

// 响应式数据
const uploadRef = ref<UploadInstance>();
const fileList = ref<UploadFiles>([]);
const selectedFile = ref<File | null>(null);
const downloadLoading = ref(false);
const importLoading = ref(false);
const importResult = ref<GradeImportResultDTO | null>(null);

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit("update:modelValue", value)
});

// 获取摘要文本
const getSummaryText = () => {
  if (!importResult.value) return "";
  const { totalRows, successRows, failedRows } = importResult.value;
  return `总计 ${totalRows} 行，成功 ${successRows} 行，失败 ${failedRows} 行`;
};

// 下载模板
const handleDownloadTemplate = async () => {
  if (!props.classCode || !props.courseCode || !props.semesterId) {
    ElMessage.error("缺少必要参数，无法下载模板");
    return;
  }

  downloadLoading.value = true;
  try {
    const response = await downloadImportTemplate(props.classCode, props.courseCode, props.semesterId);
    
    // 生成文件名
    const fallbackFileName = generateDefaultFileName(
      `成绩导入模板_${props.className}_${props.courseName}`,
      '.xlsx'
    );
    
    // 处理文件下载
    handleFileDownload(response, fallbackFileName);
    
    ElMessage.success("模板下载成功");
  } catch (error) {
    console.error("下载模板失败:", error);
    ElMessage.error("下载模板失败");
  } finally {
    downloadLoading.value = false;
  }
};

// 文件变化处理
const handleFileChange = (file: UploadFile) => {
  if (file.raw) {
    selectedFile.value = file.raw;
    importResult.value = null; // 清除之前的导入结果
  }
};

// 文件超出限制处理
const handleExceed = () => {
  ElMessage.warning("只能上传一个文件");
};

// 上传前验证
const beforeUpload = (file: File) => {
  const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                  file.type === 'application/vnd.ms-excel' ||
                  file.name.endsWith('.xlsx') ||
                  file.name.endsWith('.xls');
  
  if (!isExcel) {
    ElMessage.error('只能上传 Excel 文件！');
    return false;
  }

  const isLt10M = file.size / 1024 / 1024 < 10;
  if (!isLt10M) {
    ElMessage.error('文件大小不能超过 10MB！');
    return false;
  }

  return false; // 阻止自动上传
};

// 开始导入
const handleImport = async () => {
  if (!selectedFile.value) {
    ElMessage.error("请选择要导入的文件");
    return;
  }

  if (!props.classCode || !props.courseCode || !props.semesterId) {
    ElMessage.error("缺少必要参数，无法导入");
    return;
  }

  try {
    await ElMessageBox.confirm(
      '确定要导入成绩吗？导入过程中请勿关闭页面。',
      '确认导入',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );

    importLoading.value = true;
    importResult.value = null;

    const result = await importGrades(
      selectedFile.value,
      props.classCode,
      props.courseCode,
      props.semesterId
    );

    importResult.value = result.data;

    if (result.data.success) {
      ElMessage.success("导入完成");
      if (result.data.successRows > 0) {
        emit("success");
      }
    } else {
      ElMessage.error("导入失败，请查看错误详情");
    }

  } catch (error) {
    if (error !== 'cancel') {
      console.error("导入失败:", error);
      ElMessage.error("导入失败");
    }
  } finally {
    importLoading.value = false;
  }
};

// 关闭弹窗
const handleClose = () => {
  visible.value = false;
  // 重置状态
  fileList.value = [];
  selectedFile.value = null;
  importResult.value = null;
  uploadRef.value?.clearFiles();
};

// 监听弹窗打开，重置状态
watch(visible, (newVal) => {
  if (newVal) {
    fileList.value = [];
    selectedFile.value = null;
    importResult.value = null;
    uploadRef.value?.clearFiles();
  }
});
</script>

<style scoped>
.import-content {
  padding: 0 4px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.error-details {
  max-height: 200px;
  overflow-y: auto;
}

.error-list {
  max-height: 150px;
  overflow-y: auto;
}

.error-item {
  padding: 4px 0;
  border-bottom: 1px solid #f0f0f0;
  font-size: 13px;
  color: #e74c3c;
}

.error-item:last-child {
  border-bottom: none;
}

:deep(.el-alert__content) {
  padding-left: 8px;
}

:deep(.el-alert__content p) {
  margin: 2px 0;
  font-size: 13px;
  line-height: 1.4;
}

:deep(.el-upload-dragger) {
  padding: 40px 20px;
}
</style>
