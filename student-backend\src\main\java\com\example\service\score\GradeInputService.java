package com.example.service.score;

import com.example.common.PageResult;
import com.example.dto.score.GradeInputDTO;
import com.example.dto.score.GradeInputQueryDTO;
import com.example.dto.score.BatchGradeInputDTO;
import com.example.dto.score.GradeImportResultDTO;
import com.example.vo.score.GradeInputVO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 成绩录入服务接口
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
public interface GradeInputService {

    /**
     * 分页查询成绩录入列表
     *
     * @param params 查询参数
     * @return 分页结果
     */
    PageResult<GradeInputVO> getGradeInputPage(GradeInputQueryDTO params);

    /**
     * 录入单个成绩
     *
     * @param gradeInputDTO 成绩录入DTO
     * @return 是否成功
     */
    boolean inputGrade(GradeInputDTO gradeInputDTO);

    /**
     * 批量录入成绩
     *
     * @param batchGradeInputDTO 批量成绩录入DTO
     * @return 成功录入的数量
     */
    int batchInputGrades(BatchGradeInputDTO batchGradeInputDTO);

    /**
     * 更新成绩
     *
     * @param gradeInputDTO 成绩录入DTO
     * @return 是否成功
     */
    boolean updateGrade(GradeInputDTO gradeInputDTO);

    /**
     * 删除成绩
     *
     * @param id 成绩ID
     * @return 是否成功
     */
    boolean deleteGrade(Integer id);

    /**
     * 根据班级和课程获取学生成绩录入列表
     *
     * @param classCode  班级代码
     * @param courseCode 课程代码
     * @param semesterId 学期ID
     * @return 学生成绩录入列表
     */
    List<GradeInputVO> getStudentGradeInputList(String classCode, String courseCode, Integer semesterId);

    /**
     * 根据ID获取成绩详情
     *
     * @param id 成绩ID
     * @return 成绩详情
     */
    GradeInputVO getGradeById(Integer id);

    /**
     * 检查成绩是否已存在
     *
     * @param studentId  学号
     * @param courseCode 课程代码
     * @param semesterId 学期ID
     * @return 是否存在
     */
    boolean checkGradeExists(String studentId, String courseCode, Integer semesterId);

    /**
     * 导入成绩
     *
     * @param file       Excel文件
     * @param classCode  班级代码
     * @param courseCode 课程代码
     * @param semesterId 学期ID
     * @return 导入结果
     */
    GradeImportResultDTO importGrades(MultipartFile file, String classCode, String courseCode, Integer semesterId);

    /**
     * 生成成绩导入模板
     *
     * @param classCode  班级代码
     * @param courseCode 课程代码
     * @param semesterId 学期ID
     * @return Excel模板字节数组
     */
    byte[] generateImportTemplate(String classCode, String courseCode, Integer semesterId);

    /**
     * 获取成绩导入模板表头配置
     *
     * @return 表头配置
     */
    Map<String, String> getImportTemplateHeaders();

}
