<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.mapper.basic.ClassesMapper">

    <!-- 结果映射 -->
    <resultMap id="ClassesVOResultMap" type="com.example.vo.basic.ClassesVO">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="class_code" property="classCode" jdbcType="VARCHAR"/>
        <result column="class_name" property="className" jdbcType="VARCHAR"/>
        <result column="major_code" property="majorCode" jdbcType="VARCHAR"/>
        <result column="grade_year" property="gradeYear" jdbcType="INTEGER"/>
        <result column="student_count" property="studentCount" jdbcType="INTEGER"/>
        <result column="head_teacher_code" property="headTeacherCode" jdbcType="VARCHAR"/>
        <result column="created_at" property="createdAt" jdbcType="TIMESTAMP"/>
        <result column="updated_at" property="updatedAt" jdbcType="TIMESTAMP"/>
        <result column="major_name" property="majorName" jdbcType="VARCHAR"/>
        <result column="college_code" property="collegeCode" jdbcType="VARCHAR"/>
        <result column="head_teacher_name" property="headTeacherName" jdbcType="VARCHAR"/>
        <result column="college_name" property="collegeName" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 通用SQL片段 -->
    <sql id="Base_Column_List">
        c.id, c.class_code, c.class_name, c.major_code, c.grade_year,
        COALESCE(s.student_count, 0) as student_count, c.head_teacher_code, c.created_at, c.updated_at,
        m.major_name, col.college_code, t.name as head_teacher_name, col.college_name
    </sql>

    <!-- 通用连接查询 -->
    <sql id="Base_Join">
        FROM classes c
        LEFT JOIN majors m ON c.major_code = m.major_code
        LEFT JOIN teachers t ON c.head_teacher_code = t.teacher_code
        LEFT JOIN colleges col ON m.college_code = col.college_code
        LEFT JOIN (
            SELECT class_code, COUNT(*) as student_count
            FROM students
            WHERE status = '在校'
            GROUP BY class_code
        ) s ON c.class_code = s.class_code
    </sql>

    <!-- 查询条件 -->
    <sql id="Query_Where">
        <where>
            <if test="query.classCode != null and query.classCode != ''">
                AND c.class_code LIKE CONCAT('%', #{query.classCode}, '%')
            </if>
            <if test="query.className != null and query.className != ''">
                AND c.class_name LIKE CONCAT('%', #{query.className}, '%')
            </if>
            <if test="query.majorCode != null and query.majorCode != ''">
                AND c.major_code = #{query.majorCode}
            </if>
            <if test="query.gradeYear != null">
                AND c.grade_year = #{query.gradeYear}
            </if>
            <if test="query.headTeacherCode != null and query.headTeacherCode != ''">
                AND c.head_teacher_code = #{query.headTeacherCode}
            </if>
            <if test="query.collegeCode != null and query.collegeCode != ''">
                AND col.college_code = #{query.collegeCode}
            </if>
        </where>
    </sql>

    <!-- 分页查询班级列表 -->
    <select id="selectClassesPage" resultMap="ClassesVOResultMap">
        SELECT <include refid="Base_Column_List"/>
        <include refid="Base_Join"/>
        <include refid="Query_Where"/>
        ORDER BY
        <choose>
            <when test="query.sortField == 'collegeName'">CONVERT(col.college_name USING gbk)</when>
            <when test="query.sortField == 'majorName'">CONVERT(m.major_name USING gbk)</when>
            <when test="query.sortField == 'className'">CONVERT(c.class_name USING gbk)</when>
            <when test="query.sortField == 'classCode'">c.class_code</when>
            <when test="query.sortField == 'gradeYear'">c.grade_year</when>
            <when test="query.sortField == 'createdAt'">c.created_at</when>
            <otherwise>CONVERT(col.college_name USING gbk)</otherwise>
        </choose>
        <if test="query.sortOrder == 'desc'">DESC</if>
        <if test="query.sortOrder != 'desc'">ASC</if>
        <if test="query.sortField != 'majorName' and query.sortField != 'className'">, CONVERT(m.major_name USING gbk) ASC, CONVERT(c.class_name USING gbk) ASC</if>
        <if test="query.sortField == 'majorName'">, CONVERT(c.class_name USING gbk) ASC</if>
    </select>

    <!-- 根据ID查询班级详情 -->
    <select id="selectClassesById" resultMap="ClassesVOResultMap">
        SELECT <include refid="Base_Column_List"/>
        <include refid="Base_Join"/>
        WHERE c.id = #{id}
    </select>

    <!-- 查询所有班级列表 -->
    <select id="selectAllClasses" resultMap="ClassesVOResultMap">
        SELECT <include refid="Base_Column_List"/>
        <include refid="Base_Join"/>
        ORDER BY CONVERT(col.college_name USING gbk) ASC, CONVERT(m.major_name USING gbk) ASC, CONVERT(c.class_name USING gbk) ASC
    </select>

    <!-- 根据专业代码查询班级列表 -->
    <select id="selectClassesByMajorCode" resultMap="ClassesVOResultMap">
        SELECT <include refid="Base_Column_List"/>
        <include refid="Base_Join"/>
        WHERE c.major_code = #{majorCode}
        ORDER BY CONVERT(col.college_name USING gbk) ASC, CONVERT(m.major_name USING gbk) ASC, CONVERT(c.class_name USING gbk) ASC
    </select>

    <!-- 根据班主任工号查询班级列表 -->
    <select id="selectClassesByHeadTeacher" resultMap="ClassesVOResultMap">
        SELECT <include refid="Base_Column_List"/>
        <include refid="Base_Join"/>
        WHERE c.head_teacher_code = #{headTeacherCode}
        ORDER BY col.college_name ASC, m.major_name ASC, c.class_name ASC
    </select>

    <!-- 检查班级代码是否存在 -->
    <select id="existsClassCode" resultType="boolean">
        SELECT COUNT(1) > 0
        FROM classes
        WHERE class_code = #{classCode}
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- 更新班级学生人数 -->
    <update id="updateStudentCount">
        UPDATE classes
        SET student_count = #{studentCount}, updated_at = NOW()
        WHERE id = #{classId}
    </update>

    <!-- 获取班级在指定学期的课程列表 -->
    <select id="selectCoursesByClassAndSemester" resultType="com.example.vo.educational.CourseVO">
        SELECT DISTINCT
            c.id,
            c.course_code as courseCode,
            c.course_name as courseName,
            c.credits,
            c.course_type as courseType,
            c.description
        FROM courses c
        INNER JOIN class_courses cc ON c.course_code = cc.course_code
        WHERE cc.class_code = #{classCode}
          AND cc.semester_id = #{semesterId}
        ORDER BY c.course_name
    </select>

</mapper>
