package com.example.common.export.config;

import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.function.Function;

/**
 * 导出配置类
 * 用于配置导出的表头映射、数据处理器、文件名等信息
 *
 * <AUTHOR>
 * @since 2025-01-30
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ExportConfig {

    /**
     * 表头映射 (字段名 -> 显示名称)
     */
    @Builder.Default
    private Map<String, String> headers = new LinkedHashMap<>();

    /**
     * 工作表名称
     */
    private String sheetName;

    /**
     * 文件名前缀
     */
    private String fileNamePrefix;

    /**
     * 是否包含时间戳
     */
    @Builder.Default
    private boolean includeTimestamp = true;

    /**
     * 数据转换器
     * 用于将原始数据转换为导出格式
     */
    private Function<Object, Map<String, Object>> dataConverter;

    /**
     * 数据过滤器
     * 用于过滤不需要导出的数据
     */
    private Function<Object, Boolean> dataFilter;

    /**
     * 最大导出行数限制
     */
    @Builder.Default
    private int maxRows = 1000000;

    /**
     * 是否自动调整列宽
     */
    @Builder.Default
    private boolean autoSizeColumns = true;

    /**
     * 添加表头映射
     */
    public ExportConfig addHeader(String fieldName, String displayName) {
        if (this.headers == null) {
            this.headers = new LinkedHashMap<>();
        }
        this.headers.put(fieldName, displayName);
        return this;
    }

    /**
     * 批量添加表头映射
     */
    public ExportConfig addHeaders(Map<String, String> headers) {
        if (this.headers == null) {
            this.headers = new LinkedHashMap<>();
        }
        this.headers.putAll(headers);
        return this;
    }

    /**
     * 创建成绩导出配置
     */
    public static ExportConfig createGradeExportConfig(String className) {
        return ExportConfig.builder()
                .sheetName(className + "学业成绩")
                .fileNamePrefix("成绩导出")
                .includeTimestamp(true)
                .maxRows(1000000)
                .autoSizeColumns(true)
                .build()
                .addHeader("studentId", "学号")
                .addHeader("studentName", "姓名")
                .addHeader("className", "班级")
                .addHeader("avgScore", "平均分")
                .addHeader("academicScore", "学业成绩")
                .addHeader("calculateGpa", "绩点");
    }

    /**
     * 创建学生导出配置
     */
    public static ExportConfig createStudentExportConfig() {
        return ExportConfig.builder()
                .sheetName("学生信息")
                .fileNamePrefix("学生导出")
                .includeTimestamp(true)
                .maxRows(100000)
                .autoSizeColumns(true)
                .build()
                .addHeader("studentId", "学号")
                .addHeader("studentName", "姓名")
                .addHeader("gender", "性别")
                .addHeader("className", "班级")
                .addHeader("majorName", "专业")
                .addHeader("collegeName", "学院");
    }

    /**
     * 创建教师导出配置
     */
    public static ExportConfig createTeacherExportConfig() {
        return ExportConfig.builder()
                .sheetName("教师信息")
                .fileNamePrefix("教师导出")
                .includeTimestamp(true)
                .maxRows(50000)
                .autoSizeColumns(true)
                .build()
                .addHeader("teacherId", "工号")
                .addHeader("teacherName", "姓名")
                .addHeader("gender", "性别")
                .addHeader("title", "职称")
                .addHeader("deptName", "部门");
    }

    /**
     * 验证配置是否有效
     */
    public boolean isValid() {
        return headers != null && !headers.isEmpty() 
               && sheetName != null && !sheetName.trim().isEmpty()
               && fileNamePrefix != null && !fileNamePrefix.trim().isEmpty()
               && maxRows > 0;
    }
}
