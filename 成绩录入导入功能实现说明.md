# 成绩录入导入功能实现说明

## 功能概述

为成绩录入模块添加了Excel导入功能和模板下载功能，支持批量导入学生成绩数据。

## 实现的功能

### 1. 模板下载功能
- **接口**: `GET /api/score/input/template`
- **参数**: classCode（班级代码）、courseCode（课程代码）、semesterId（学期ID）
- **功能**: 生成并下载成绩导入模板Excel文件

### 2. 成绩导入功能
- **接口**: `POST /api/score/input/import`
- **参数**: file（Excel文件）、classCode、courseCode、semesterId
- **功能**: 解析Excel文件并批量导入成绩数据

### 3. 前端界面增强
- 在成绩录入页面添加了"导入成绩"按钮
- 实现了导入弹窗组件，支持：
  - 模板下载
  - 文件上传（拖拽上传）
  - 导入结果显示
  - 错误详情查看

## 技术实现

### 后端实现

#### 1. 扩展ApachePoiExportService
- 添加了`generateTemplate()`方法用于生成Excel模板
- 添加了`importExcelData()`方法用于解析Excel文件
- 支持数据验证和错误处理

#### 2. 新增DTO类
- `GradeImportRequestDTO`: 成绩导入数据传输对象
- `GradeImportResultDTO`: 导入结果数据传输对象

#### 3. 扩展GradeInputService
- `importGrades()`: 导入成绩主方法
- `generateImportTemplate()`: 生成导入模板
- `getImportTemplateHeaders()`: 获取模板表头配置

#### 4. 扩展GradeInputController
- 添加导入接口和模板下载接口
- 支持文件上传和响应头设置

### 前端实现

#### 1. 新增ImportDialog组件
- 支持模板下载
- 支持文件拖拽上传
- 显示导入进度和结果
- 错误详情展示

#### 2. 扩展API接口
- `downloadImportTemplate()`: 下载模板
- `importGrades()`: 导入成绩
- 使用`beforeResponseCallback`处理文件下载响应

#### 3. 界面集成
- 在GradeInputList组件中添加导入按钮
- 集成导入弹窗和成功回调

## Excel模板格式

| 字段名 | 说明 | 必填 | 格式 |
|--------|------|------|------|
| 学号 | 学生学号 | 是 | 文本 |
| 课程代码 | 课程代码 | 是 | 文本 |
| 学期ID | 学期标识 | 是 | 数字 |
| 期末成绩 | 期末成绩 | 是 | 0-100数字 |
| 是否重修 | 重修标记 | 否 | 是/否、true/false、1/0 |
| 备注 | 备注信息 | 否 | 文本 |

## 数据验证

### 1. 文件验证
- 文件格式：仅支持.xlsx和.xls
- 文件大小：最大10MB
- 表头验证：检查必需的表头是否存在

### 2. 数据验证
- 学号、课程代码、学期ID：必填验证
- 期末成绩：范围验证（0-100）
- 重复性检查：同一学生同一课程同一学期不能重复录入

### 3. 业务验证
- 绩点自动计算
- 成绩存在性检查
- 数据库约束验证

## 错误处理

### 1. 导入结果统计
- 总行数、成功行数、失败行数
- 详细错误信息列表
- 错误行号和学号定位

### 2. 错误类型
- 文件格式错误
- 数据验证错误
- 业务规则错误
- 数据库操作错误

## 使用流程

1. **进入成绩录入页面**
   - 选择专业 → 班级 → 课程 → 学期

2. **下载模板**
   - 点击"导入成绩"按钮
   - 在弹窗中点击"下载导入模板"
   - 获得包含正确表头的Excel模板

3. **填写数据**
   - 在模板中填写学生成绩数据
   - 确保必填字段完整
   - 检查数据格式正确

4. **导入数据**
   - 在导入弹窗中上传填好的Excel文件
   - 点击"开始导入"
   - 查看导入结果和错误详情

5. **处理错误**
   - 根据错误提示修正数据
   - 重新导入失败的记录

## 注意事项

1. **权限控制**: 导入功能需要相应的权限
2. **数据备份**: 建议在大批量导入前备份数据
3. **并发控制**: 避免同时导入相同的数据
4. **性能考虑**: 大文件导入可能需要较长时间
5. **错误恢复**: 导入失败时不会影响已成功的数据

## 技术特点

1. **流式处理**: 使用Apache POI的SXSSF支持大文件处理
2. **事务控制**: 导入过程使用事务确保数据一致性
3. **详细日志**: 记录导入过程和错误信息
4. **用户友好**: 提供直观的操作界面和错误提示
5. **扩展性**: 代码结构支持后续功能扩展
