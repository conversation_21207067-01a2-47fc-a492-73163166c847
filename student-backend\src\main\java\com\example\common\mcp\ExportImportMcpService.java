package com.example.common.mcp;

import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 导出导入MCP服务接口
 * 统一处理所有导出导入功能，消除重复代码
 *
 * <AUTHOR>
 * @since 2025-01-30
 */
public interface ExportImportMcpService {

    /**
     * 同步导出数据
     *
     * @param config 导出配置
     * @param <T>    数据类型
     * @return 导出文件响应
     */
    <T> ResponseEntity<Resource> exportData(ExportConfig<T> config);

    /**
     * 异步导出数据（适用于大数据量）
     *
     * @param config 导出配置
     * @param <T>    数据类型
     * @return 异步任务
     */
    <T> CompletableFuture<ResponseEntity<Resource>> exportDataAsync(ExportConfig<T> config);

    /**
     * 导入数据
     *
     * @param file   上传文件
     * @param config 导入配置
     * @param <T>    数据类型
     * @return 导入结果
     */
    <T> ImportResult<T> importData(MultipartFile file, ImportConfig<T> config);

    /**
     * 生成导入模板
     *
     * @param config 模板配置
     * @param <T>    数据类型
     * @return 模板文件响应
     */
    <T> ResponseEntity<Resource> generateTemplate(TemplateConfig<T> config);

    /**
     * 验证导入文件格式
     *
     * @param file 上传文件
     * @return 验证结果
     */
    ValidationResult validateFile(MultipartFile file);

    /**
     * 导出配置接口
     */
    interface ExportConfig<T> {
        /**
         * 获取数据源
         */
        List<T> getData();

        /**
         * 获取字段映射
         */
        Map<String, String> getFieldMapping();

        /**
         * 获取文件名前缀
         */
        String getFileNamePrefix();

        /**
         * 获取工作表名称
         */
        String getSheetName();

        /**
         * 获取导出格式
         */
        ExportFormat getFormat();

        /**
         * 是否包含时间戳
         */
        default boolean includeTimestamp() {
            return true;
        }

        /**
         * 获取额外参数
         */
        default Map<String, Object> getExtraParams() {
            return Map.of();
        }
    }

    /**
     * 导入配置接口
     */
    interface ImportConfig<T> {
        /**
         * 获取字段映射
         */
        Map<String, String> getFieldMapping();

        /**
         * 获取数据构建器
         */
        DataBuilder<T> getDataBuilder();

        /**
         * 获取数据验证器
         */
        DataValidator<T> getValidator();

        /**
         * 获取数据保存器
         */
        DataSaver<T> getSaver();

        /**
         * 获取工作表名称
         */
        default String getSheetName() {
            return "Sheet1";
        }

        /**
         * 是否跳过空行
         */
        default boolean skipEmptyRows() {
            return true;
        }
    }

    /**
     * 模板配置接口
     */
    interface TemplateConfig<T> {
        /**
         * 获取字段映射
         */
        Map<String, String> getFieldMapping();

        /**
         * 获取文件名前缀
         */
        String getFileNamePrefix();

        /**
         * 获取工作表名称
         */
        String getSheetName();

        /**
         * 获取示例数据
         */
        default List<T> getSampleData() {
            return List.of();
        }
    }

    /**
     * 数据构建器
     */
    @FunctionalInterface
    interface DataBuilder<T> {
        T build(Map<String, Object> rowData);
    }

    /**
     * 数据验证器
     */
    @FunctionalInterface
    interface DataValidator<T> {
        ValidationResult validate(T data);
    }

    /**
     * 数据保存器
     */
    @FunctionalInterface
    interface DataSaver<T> {
        void save(List<T> dataList);
    }

    /**
     * 导出格式枚举
     */
    enum ExportFormat {
        EXCEL_XLSX,
        EXCEL_XLS,
        CSV,
        JSON
    }

    /**
     * 导入结果
     */
    class ImportResult<T> {
        private boolean success;
        private String message;
        private int successCount;
        private int errorCount;
        private List<T> successData;
        private List<ImportError> errors;

        // 构造方法、getter、setter省略
        public static <T> ImportResult<T> success(List<T> data, String message) {
            ImportResult<T> result = new ImportResult<>();
            result.success = true;
            result.successData = data;
            result.successCount = data.size();
            result.errorCount = 0;
            result.message = message;
            return result;
        }

        public static <T> ImportResult<T> error(String message) {
            ImportResult<T> result = new ImportResult<>();
            result.success = false;
            result.message = message;
            return result;
        }

        // getters and setters
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        public int getSuccessCount() { return successCount; }
        public void setSuccessCount(int successCount) { this.successCount = successCount; }
        public int getErrorCount() { return errorCount; }
        public void setErrorCount(int errorCount) { this.errorCount = errorCount; }
        public List<T> getSuccessData() { return successData; }
        public void setSuccessData(List<T> successData) { this.successData = successData; }
        public List<ImportError> getErrors() { return errors; }
        public void setErrors(List<ImportError> errors) { this.errors = errors; }
    }

    /**
     * 导入错误信息
     */
    class ImportError {
        private int rowNumber;
        private String errorMessage;

        public ImportError(int rowNumber, String errorMessage) {
            this.rowNumber = rowNumber;
            this.errorMessage = errorMessage;
        }

        // getters and setters
        public int getRowNumber() { return rowNumber; }
        public void setRowNumber(int rowNumber) { this.rowNumber = rowNumber; }
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
    }

    /**
     * 验证结果
     */
    class ValidationResult {
        private boolean valid;
        private String errorMessage;

        public static ValidationResult success() {
            ValidationResult result = new ValidationResult();
            result.valid = true;
            return result;
        }

        public static ValidationResult error(String message) {
            ValidationResult result = new ValidationResult();
            result.valid = false;
            result.errorMessage = message;
            return result;
        }

        // getters and setters
        public boolean isValid() { return valid; }
        public void setValid(boolean valid) { this.valid = valid; }
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
    }
}
