package com.example.common.export.config;

import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Set;
import java.util.HashSet;
import java.util.function.Function;

/**
 * 导入配置类
 * 用于配置导入的表头映射、数据验证器、转换器等信息
 *
 * <AUTHOR>
 * @since 2025-01-30
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ImportConfig {

    /**
     * 表头映射 (字段名 -> 显示名称)
     */
    @Builder.Default
    private Map<String, String> headers = new LinkedHashMap<>();

    /**
     * 必填字段集合
     */
    @Builder.Default
    private Set<String> requiredFields = new HashSet<>();

    /**
     * 工作表名称
     */
    private String sheetName;

    /**
     * 模板文件名前缀
     */
    private String templateFileNamePrefix;

    /**
     * 最大导入行数限制
     */
    @Builder.Default
    private int maxRows = 10000;

    /**
     * 最大文件大小（字节）
     */
    @Builder.Default
    private long maxFileSize = 10 * 1024 * 1024; // 10MB

    /**
     * 数据转换器
     * 用于将Excel数据转换为业务对象
     */
    private Function<Map<String, Object>, Object> dataConverter;

    /**
     * 数据验证器
     * 用于验证导入数据的有效性
     */
    private Function<Object, ValidationResult> dataValidator;

    /**
     * 是否跳过空行
     */
    @Builder.Default
    private boolean skipEmptyRows = true;

    /**
     * 是否严格模式（表头必须完全匹配）
     */
    @Builder.Default
    private boolean strictMode = false;

    /**
     * 添加表头映射
     */
    public ImportConfig addHeader(String fieldName, String displayName) {
        if (this.headers == null) {
            this.headers = new LinkedHashMap<>();
        }
        this.headers.put(fieldName, displayName);
        return this;
    }

    /**
     * 添加表头映射并标记为必填
     */
    public ImportConfig addRequiredHeader(String fieldName, String displayName) {
        addHeader(fieldName, displayName);
        if (this.requiredFields == null) {
            this.requiredFields = new HashSet<>();
        }
        this.requiredFields.add(fieldName);
        return this;
    }

    /**
     * 批量添加表头映射
     */
    public ImportConfig addHeaders(Map<String, String> headers) {
        if (this.headers == null) {
            this.headers = new LinkedHashMap<>();
        }
        this.headers.putAll(headers);
        return this;
    }

    /**
     * 添加必填字段
     */
    public ImportConfig addRequiredField(String fieldName) {
        if (this.requiredFields == null) {
            this.requiredFields = new HashSet<>();
        }
        this.requiredFields.add(fieldName);
        return this;
    }

    /**
     * 创建成绩导入配置
     */
    public static ImportConfig createGradeImportConfig() {
        return ImportConfig.builder()
                .sheetName("成绩导入")
                .templateFileNamePrefix("成绩导入模板")
                .maxRows(5000)
                .maxFileSize(5 * 1024 * 1024) // 5MB
                .skipEmptyRows(true)
                .strictMode(true)
                .build()
                .addRequiredHeader("studentId", "学号")
                .addRequiredHeader("courseCode", "课程代码")
                .addRequiredHeader("semesterId", "学期ID")
                .addRequiredHeader("finalScore", "期末成绩")
                .addHeader("remarks", "备注");
    }

    /**
     * 创建学生导入配置
     */
    public static ImportConfig createStudentImportConfig() {
        return ImportConfig.builder()
                .sheetName("学生导入")
                .templateFileNamePrefix("学生导入模板")
                .maxRows(10000)
                .maxFileSize(10 * 1024 * 1024) // 10MB
                .skipEmptyRows(true)
                .strictMode(false)
                .build()
                .addRequiredHeader("studentId", "学号")
                .addRequiredHeader("studentName", "姓名")
                .addRequiredHeader("gender", "性别")
                .addRequiredHeader("className", "班级")
                .addHeader("phone", "电话")
                .addHeader("email", "邮箱");
    }

    /**
     * 创建教师导入配置
     */
    public static ImportConfig createTeacherImportConfig() {
        return ImportConfig.builder()
                .sheetName("教师导入")
                .templateFileNamePrefix("教师导入模板")
                .maxRows(5000)
                .maxFileSize(5 * 1024 * 1024) // 5MB
                .skipEmptyRows(true)
                .strictMode(false)
                .build()
                .addRequiredHeader("teacherId", "工号")
                .addRequiredHeader("teacherName", "姓名")
                .addRequiredHeader("gender", "性别")
                .addHeader("title", "职称")
                .addHeader("phone", "电话")
                .addHeader("email", "邮箱");
    }

    /**
     * 验证配置是否有效
     */
    public boolean isValid() {
        return headers != null && !headers.isEmpty() 
               && sheetName != null && !sheetName.trim().isEmpty()
               && templateFileNamePrefix != null && !templateFileNamePrefix.trim().isEmpty()
               && maxRows > 0 && maxFileSize > 0;
    }

    /**
     * 验证结果类
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ValidationResult {
        private boolean valid;
        private String errorMessage;
        private int rowNumber;

        public static ValidationResult success() {
            return ValidationResult.builder().valid(true).build();
        }

        public static ValidationResult error(String message, int rowNumber) {
            return ValidationResult.builder()
                    .valid(false)
                    .errorMessage(message)
                    .rowNumber(rowNumber)
                    .build();
        }
    }
}
