<template>
  <el-dialog
    v-model="visible"
    title="批量录入成绩"
    width="80%"
    :before-close="handleClose"
  >
    <div class="batch-input-container">
      <div class="info-section">
        <el-descriptions :column="3" border>
          <el-descriptions-item label="班级">{{ className }}</el-descriptions-item>
          <el-descriptions-item label="课程">{{ courseName }}</el-descriptions-item>
          <el-descriptions-item label="学期">{{ semesterName }}</el-descriptions-item>
        </el-descriptions>
      </div>

      <div class="table-section">
        <div class="table-header">
          <span>学生成绩列表</span>
          <el-space>
            <el-button type="warning" @click="handleClearAll">
              清空所有成绩
            </el-button>
          </el-space>
        </div>

        <el-table
          :data="studentList"
          border
          stripe
          height="400"
          style="width: 100%"
        >
          <el-table-column type="index" label="序号" width="60" align="center" />
          <el-table-column prop="studentId" label="学号" width="120" align="center" />
          <el-table-column prop="studentName" label="姓名" width="100" align="center" />
          <el-table-column label="期末成绩" width="150" align="center">
            <template #default="{ row, $index }">
              <el-input-number
                v-model="row.finalScore"
                :min="0"
                :max="100"
                :precision="2"
                :step="0.1"
                placeholder="请输入成绩"
                size="small"
                style="width: 100%"
              />
            </template>
          </el-table-column>
          <el-table-column label="绩点" width="120" align="center">
            <template #default="{ row }">
              <el-input
                v-model="row.gradePoint"
                placeholder="自动计算"
                size="small"
                disabled
                style="width: 100%"
              />
            </template>
          </el-table-column>
          <el-table-column label="是否重修" width="100" align="center">
            <template #default="{ row }">
              <el-switch v-model="row.isRetake" size="small" />
            </template>
          </el-table-column>
          <el-table-column label="备注" min-width="150">
            <template #default="{ row }">
              <el-input
                v-model="row.remarks"
                placeholder="请输入备注"
                size="small"
              />
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleSubmit">
          批量提交
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from "vue";
import { message } from "@/utils/message";
import { getStudentGradeInputList, batchInputGrades } from "@/api/score/grade-input";
import { getAllSemesters } from "@/api/basic/semester";
import type { GradeInputVO, BatchGradeInputDTO, GradeInputDTO } from "@/api/score/grade-input";

defineOptions({
  name: "BatchInputDialog"
});

// Props
interface Props {
  modelValue: boolean;
  classCode: string;
  className: string;
  courseCode: string;
  courseName: string;
  semesterId?: number | null;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  classCode: "",
  className: "",
  courseCode: "",
  courseName: "",
  semesterId: null
});

// Emits
const emit = defineEmits<{
  "update:modelValue": [value: boolean];
  success: [];
}>();

// 响应式数据
const loading = ref(false);
const studentList = ref<(GradeInputVO & { finalScore?: number; gradePoint?: number })[]>([]);
const semesters = ref([]);

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit("update:modelValue", value)
});

const semesterName = computed(() => {
  const semester = semesters.value.find((s: any) => s.id === props.semesterId);
  return semester?.semesterName || "";
});

// 监听弹窗显示
watch(visible, (newVal) => {
  if (newVal) {
    initData();
  }
});

// 初始化数据
const initData = async () => {
  await getSemesters();
  await getStudentList();
};

// 获取学期列表
const getSemesters = async () => {
  try {
    const { data } = await getAllSemesters();
    semesters.value = data || [];
  } catch (error) {
    console.error("获取学期列表失败:", error);
  }
};

// 获取学生列表
const getStudentList = async () => {
  if (!props.classCode || !props.courseCode || !props.semesterId) {
    return;
  }

  try {
    const { data } = await getStudentGradeInputList(props.classCode, props.courseCode, props.semesterId);
    studentList.value = data.map(student => ({
      ...student,
      finalScore: student.finalScore || undefined,
      gradePoint: student.gradePoint || undefined
    }));
  } catch (error) {
    console.error("获取学生列表失败:", error);
    message("获取学生列表失败", { type: "error" });
  }
};

// 绩点由后端自动计算，前端不再处理

// 清空所有成绩
const handleClearAll = () => {
  studentList.value.forEach(student => {
    student.finalScore = undefined;
    student.gradePoint = undefined;
    student.isRetake = false;
    student.remarks = "";
  });
  message("已清空所有成绩", { type: "info" });
};

// 提交表单
const handleSubmit = async () => {
  // 过滤出有成绩的学生
  const validGrades = studentList.value.filter(student =>
    student.finalScore !== null &&
    student.finalScore !== undefined &&
    student.finalScore >= 0 &&
    student.finalScore <= 100
  );

  if (validGrades.length === 0) {
    message("请至少录入一个学生的成绩", { type: "warning" });
    return;
  }

  try {
    loading.value = true;

    const batchData: BatchGradeInputDTO = {
      classCode: props.classCode,
      courseCode: props.courseCode,
      semesterId: props.semesterId!,
      grades: validGrades.map(student => ({
        id: student.id,
        studentId: student.studentId,
        courseCode: props.courseCode,
        semesterId: props.semesterId!,
        finalScore: student.finalScore!,
        gradePoint: student.gradePoint,
        isRetake: student.isRetake || false,
        remarks: student.remarks || ""
      }))
    };

    const { data } = await batchInputGrades(batchData);
    message(`批量录入成功，共录入${data}条记录`, { type: "success" });

    emit("success");
    handleClose();
  } catch (error) {
    console.error("批量录入失败:", error);
    message("批量录入失败", { type: "error" });
  } finally {
    loading.value = false;
  }
};

// 关闭弹窗
const handleClose = () => {
  studentList.value = [];
  visible.value = false;
};
</script>

<style scoped>
.batch-input-container {
  padding: 10px 0;
}

.info-section {
  margin-bottom: 20px;
}

.table-section {
  margin-top: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  font-weight: bold;
}

.dialog-footer {
  text-align: right;
}
</style>
