package com.example.controller.score;

import com.example.common.PageResult;
import com.example.common.Result;
import com.example.common.export.utils.FileResponseUtils;
import com.example.dto.score.GradeQueryDTO;
import com.example.service.score.FinalGradeService;
import com.example.vo.score.GradeVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 期末成绩控制器
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Tag(name = "期末成绩管理", description = "期末成绩相关接口")
@RestController
@RequestMapping("/api/score/final")
@RequiredArgsConstructor
@Slf4j
@Validated
public class FinalGradeController {

    private final FinalGradeService finalGradeService;

    @Operation(summary = "分页查询期末成绩列表")
    @PostMapping("/page")
    public Result<PageResult<GradeVO>> getGradePage(@RequestBody GradeQueryDTO params) {
        try {
            PageResult<GradeVO> result = finalGradeService.getGradePage(params);
            return Result.success(result);
        } catch (Exception e) {
            log.error("查询期末成绩列表失败", e);
            return Result.error("查询期末成绩列表失败: " + e.getMessage());
        }
    }

    @Operation(summary = "根据班级查询期末成绩")
    @GetMapping("/class/{classCode}")
    public Result<List<GradeVO>> getGradesByClass(
            @Parameter(description = "班级代码") @PathVariable String classCode,
            @Parameter(description = "学期ID") @RequestParam(required = false) Integer semesterId) {
        try {
            List<GradeVO> grades = finalGradeService.getGradesByClass(classCode, semesterId);
            return Result.success(grades);
        } catch (Exception e) {
            log.error("根据班级查询期末成绩失败", e);
            return Result.error("根据班级查询期末成绩失败: " + e.getMessage());
        }
    }

    @Operation(summary = "根据学生查询期末成绩")
    @GetMapping("/student/{studentId}")
    public Result<List<GradeVO>> getGradesByStudent(
            @Parameter(description = "学号") @PathVariable String studentId,
            @Parameter(description = "学期ID") @RequestParam(required = false) Integer semesterId) {
        try {
            List<GradeVO> grades = finalGradeService.getGradesByStudent(studentId, semesterId);
            return Result.success(grades);
        } catch (Exception e) {
            log.error("根据学生查询期末成绩失败", e);
            return Result.error("根据学生查询期末成绩失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取横向期末成绩数据")
    @GetMapping("/horizontal")
    public Result<Map<String, Object>> getHorizontalGrades(
            @Parameter(description = "班级代码") @RequestParam String classCode,
            @Parameter(description = "学期ID") @RequestParam(required = false) Integer semesterId,
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer pageNum,
            @Parameter(description = "页大小") @RequestParam(defaultValue = "10") Integer pageSize) {
        try {
            Map<String, Object> result = finalGradeService.getHorizontalGrades(classCode, semesterId, pageNum, pageSize);
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取横向期末成绩失败", e);
            return Result.error("获取横向期末成绩失败: " + e.getMessage());
        }
    }

    @Operation(summary = "导出期末成绩Excel")
    @GetMapping("/export")
    public ResponseEntity<Resource> exportGrades(
            @Parameter(description = "班级代码") @RequestParam String classCode,
            @Parameter(description = "学期ID") @RequestParam(required = false) Integer semesterId) {
        try {
            byte[] excelData = finalGradeService.exportGrades(classCode, semesterId);

            // 获取班级名称
            String className = finalGradeService.getClassNameByCode(classCode);
            if (className == null || className.trim().isEmpty()) {
                className = classCode; // 如果找不到班级名称，使用班级代码
            }

            // 生成文件名
            String baseName = "期末成绩_" + className;
            if (semesterId != null) {
                baseName += "_学期" + semesterId;
            }

            // 使用FileResponseUtils创建响应
            return FileResponseUtils.createExcelDownloadResponse(excelData, baseName);

        } catch (Exception e) {
            log.error("导出期末成绩失败", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    @Operation(summary = "导出多学期合并期末成绩Excel")
    @GetMapping("/export/multiple")
    public ResponseEntity<Resource> exportMultipleSemestersGrades(
            @Parameter(description = "班级代码") @RequestParam String classCode,
            @Parameter(description = "学期ID列表") @RequestParam List<Integer> semesterIds) {
        try {
            byte[] excelData = finalGradeService.exportMultipleSemestersGrades(classCode, semesterIds);

            // 获取班级名称
            String className = finalGradeService.getClassNameByCode(classCode);
            if (className == null || className.trim().isEmpty()) {
                className = classCode; // 如果找不到班级名称，使用班级代码
            }

            // 生成文件名
            String baseName = className + "_学业成绩_多学期";

            // 使用FileResponseUtils创建响应
            return FileResponseUtils.createExcelDownloadResponse(excelData, baseName);

        } catch (Exception e) {
            log.error("导出多学期合并期末成绩失败", e);
            return ResponseEntity.internalServerError().build();
        }
    }
}
